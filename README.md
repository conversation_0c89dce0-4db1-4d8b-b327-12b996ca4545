# Satya Samadhan Platform

This repository contains three React Native mobile applications built with Expo for the Satya Samadhan platform:

1. **<PERSON><PERSON><PERSON> Officer App** - For law enforcement officers
2. **<PERSON><PERSON><PERSON>han Forensic App** - For forensic experts
3. **Sa<PERSON>a Samadhan Judiciary App** - For judicial officials

## Project Structure

```
satya-samadhan/
├── daakia-satya-samadhan-app-officer/    # Officer application
├── daakia-satya-samadhan-app-forensic/   # Forensic application
└── daakia-satya-samadhan-app-judicary/   # Judiciary application
```

## Technology Stack

- **Framework**: React Native with Expo (SDK 52)
- **Navigation**: Expo Router v4
- **Environment Management**: dotenv
- **Build & Deployment**: EAS (Expo Application Services)

## Important SDK 52 Requirements

- **Splash Screen Configuration**: Must have proper configuration in app.json for `expo-splash-screen` as this is mandatory in SDK 52
- **New Architecture Compatibility**: Some libraries may have compatibility issues with New Architecture mode
- **Web Support**: Using react-native-web 0.19.x for web compatibility

## Getting Started

Each application has its own setup and configuration. Please refer to the individual README files in each application directory for specific instructions:

- [Officer App README](./daakia-satya-samadhan-app-officer/README.md)
- [Forensic App README](./daakia-satya-samadhan-app-forensic/README.md)
- [Judiciary App README](./daakia-satya-samadhan-app-judicary/README.md)

## Common Development Commands

```bash
# Install dependencies
npm install

# Start the development server
npx expo start

# Run on Android
npx expo start --android

# Run on iOS
npx expo start --ios

# Run on web
npx expo start --web
```

## Environment Configuration

Each app uses environment variables for configuration. Create appropriate `.env` files based on the environment:
- `.env.development` - Development environment
- `.env.preview` - Preview/staging environment
- `.env.production` - Production environment

## Building Applications

All applications use EAS (Expo Application Services) for building. Refer to the `eas.json` file in each app directory for build profiles.

```bash
# Build for development
eas build --profile development

# Build for preview
eas build --profile preview

# Build for production
eas build --profile production
```

## Troubleshooting SDK 52 Issues

If you encounter problems with:
- Splash screens not working properly
- Navigation glitches or screen flickering on Android
- Image manipulation performance on iOS
- Screen orientation not locking on iOS

Refer to [Expo GitHub Issues](https://github.com/expo/expo/issues) for latest information and workarounds.

## License

[Add your license information here]

## Contact

[Add contact information here]