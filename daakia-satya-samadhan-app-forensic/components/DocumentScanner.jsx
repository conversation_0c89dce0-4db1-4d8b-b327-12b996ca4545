import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Image, Alert, ActivityIndicator, Modal, Dimensions } from 'react-native';
import { DocumentScanner } from 'react-native-document-scanner-plugin';
import { Colors } from '../constants/colors';
import { Ionicons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

const DocumentScannerComponent = ({ onComplete, onClose }) => {
  const [isScanning, setIsScanning] = useState(false);
  const [scannedImages, setScannedImages] = useState([]);
  const [quality, setQuality] = useState('high');
  const documentTypes = ['ID Card', 'Passport', 'Receipt', 'Other'];
  const [selectedType, setSelectedType] = useState('Other');
  const [edgesDetected, setEdgesDetected] = useState(false);
  const [isStable, setIsStable] = useState(false);

  const startScanning = async () => {
    try {
      setIsScanning(true);
      const { scannedImages } = await DocumentScanner.scanDocument({
        maxNumDocuments: 5,
        detectorAspectRatio: 1.0,
        detectCorners: true,
        useBase64: true,
        saveInAppDocument: false,
        brightness: 0.7,
        contrast: 1.3,
        qualityRatio: 0.9,
        saturation: 1.0,
        edgeDetectionMode: 'accurate',
        edgeDetectionThreshold: 0.1,
        manualOnly: false,
        onEdgeDetection: (result) => {
          setEdgesDetected(result.allEdgesDetected);
        },
      });

      if (!scannedImages || scannedImages.length === 0) {
        throw new Error('No image was scanned');
      }

      // Check file size (assuming base64 string)
      const fileSizeInMB = (scannedImages[0].length * 3/4) / (1024*1024);
      if (fileSizeInMB > 10) { // 10MB limit
        throw new Error('Scanned image is too large (max 10MB)');
      }

      setScannedImages(scannedImages);
    } catch (error) {
      console.error('Error scanning document:', error);
      let errorMessage = 'Failed to scan document. Please try again.';
      
      if (error.message.includes('permission')) {
        errorMessage = 'Camera permission denied. Please enable camera access in settings.';
      } else if (error.message.includes('cancelled')) {
        errorMessage = 'Scanning was cancelled';
      } else if (error.message.includes('too large')) {
        errorMessage = 'Image file is too large. Please try again with a smaller document.';
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setIsScanning(false);
    }
  };

  const handleAccept = () => {
    if (scannedImages.length > 0) {
      onComplete(scannedImages);
      onClose();
    }
  };

  const handleRetry = () => {
    setScannedImages([]);
  };

  return (
    <Modal
      visible={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={24} color={Colors.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>Document Scanner</Text>
        </View>

        {!scannedImages.length ? (
          <View style={styles.placeholderContainer}>
            <View style={styles.scanFrame}>
              <EdgeDetectionGuide />
            </View>
            <Text style={styles.instructions}>
              Tap the scan button to capture a document
            </Text>
          </View>
        ) : (
          <View style={styles.previewContainer}>
            <Image
              source={{ uri: `data:image/jpeg;base64,${scannedImages[0]}` }}
              style={styles.preview}
              resizeMode="contain"
            />
            <View style={styles.previewControls}>
              <TouchableOpacity
                style={[styles.button, styles.retryButton]}
                onPress={handleRetry}
              >
                <Ionicons name="refresh" size={20} color="white" />
                <Text style={styles.buttonText}>Retry</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.acceptButton]}
                onPress={handleAccept}
              >
                <Ionicons name="checkmark" size={20} color="white" />
                <Text style={styles.buttonText}>Accept</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {!scannedImages.length && (
          <TouchableOpacity
            style={styles.scanButton}
            onPress={startScanning}
            disabled={isScanning}
          >
            {isScanning ? (
              <ActivityIndicator color="white" />
            ) : (
              <>
                <Ionicons name="scan-outline" size={24} color="white" />
                <Text style={styles.scanButtonText}>Scan Document</Text>
              </>
            )}
          </TouchableOpacity>
        )}

        <View style={styles.qualitySelector}>
          <Text>Quality:</Text>
          <TouchableOpacity 
            style={[styles.qualityButton, quality === 'high' && styles.selectedQuality]}
            onPress={() => setQuality('high')}>
            <Text>High</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.qualityButton, quality === 'medium' && styles.selectedQuality]}
            onPress={() => setQuality('medium')}>
            <Text>Medium</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.typeSelector}>
          {documentTypes.map(type => (
            <TouchableOpacity
              key={type}
              style={[styles.typeButton, selectedType === type && styles.selectedType]}
              onPress={() => setSelectedType(type)}>
              <Text>{type}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Modal>
  );
};

const EdgeDetectionGuide = () => (
  <View style={styles.edgeGuideContainer}>
    <View style={styles.cornerTL} />
    <View style={styles.cornerTR} />
    <View style={styles.cornerBL} />
    <View style={styles.cornerBR} />
    <Text style={styles.edgeGuideText}>
      Align document edges with the corners
    </Text>
  </View>
);

const StabilizationGuide = () => (
  <View style={[styles.stabilizationGuide, isStable && styles.stableGuide]}>
    <Text style={styles.stabilizationText}>
      {isStable ? 'Hold Still!' : 'Stabilize Device'}
    </Text>
  </View>
);

const LightingIndicator = () => {
  const [lightLevel, setLightLevel] = useState('good'); // 'poor', 'good', 'too_bright'

  return (
    <View style={styles.lightingIndicator}>
      <Ionicons 
        name={lightLevel === 'good' ? 'sunny' : 'warning'} 
        size={24} 
        color={lightLevel === 'good' ? '#2ecc71' : '#f1c40f'} 
      />
      <Text style={styles.lightingText}>
        {lightLevel === 'good' ? 'Good Lighting' : 'Adjust Lighting'}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: Colors.primary,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.background,
    marginLeft: 16,
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  scanFrame: {
    width: width * 0.8,
    height: height * 0.4,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: Colors.primary,
    backgroundColor: 'transparent',
    borderRadius: 10,
  },
  instructions: {
    color: '#666',
    fontSize: 16,
    marginTop: 20,
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  previewContainer: {
    flex: 1,
    padding: 16,
  },
  preview: {
    flex: 1,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
  },
  previewControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 8,
  },
  retryButton: {
    backgroundColor: '#fa5252',
  },
  acceptButton: {
    backgroundColor: '#40c057',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  scanButton: {
    flexDirection: 'row',
    backgroundColor: Colors.primary,
    padding: 16,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 16,
  },
  scanButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  qualitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  qualityButton: {
    padding: 8,
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: 8,
  },
  selectedQuality: {
    backgroundColor: Colors.primary,
  },
  typeSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  typeButton: {
    padding: 8,
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: 8,
  },
  selectedType: {
    backgroundColor: Colors.primary,
  },
  edgeGuideContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  cornerTL: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 40,
    height: 40,
    borderTopWidth: 3,
    borderLeftWidth: 3,
    borderColor: '#2ecc71',
  },
  cornerTR: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 40,
    height: 40,
    borderTopWidth: 3,
    borderRightWidth: 3,
    borderColor: '#2ecc71',
  },
  cornerBL: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: 40,
    height: 40,
    borderBottomWidth: 3,
    borderLeftWidth: 3,
    borderColor: '#2ecc71',
  },
  cornerBR: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 40,
    height: 40,
    borderBottomWidth: 3,
    borderRightWidth: 3,
    borderColor: '#2ecc71',
  },
  edgeGuideText: {
    position: 'absolute',
    bottom: -30,
    left: 0,
    right: 0,
    textAlign: 'center',
    color: '#2ecc71',
    fontSize: 14,
  },
  lightingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  lightingText: {
    color: '#666',
    fontSize: 16,
    marginLeft: 8,
  },
  stabilizationGuide: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 10,
  },
  stableGuide: {
    backgroundColor: '#2ecc71',
  },
  stabilizationText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default DocumentScannerComponent; 