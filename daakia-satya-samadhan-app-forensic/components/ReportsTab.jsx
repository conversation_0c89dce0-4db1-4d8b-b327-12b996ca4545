import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  ActivityIndicator,
  Modal,
  TextInput,
  FlatList,
  Alert,
} from 'react-native';
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { transformUrl } from "../utils/transformUrl";
import { Colors } from "../constants/colors";
import PreviewComponent from './Larges/PreviewComponent';
import Constants from 'expo-constants';
import { useAuth } from "../context/auth-context";

const BASE_URL = Constants.expoConfig.extra.baseUrl;

const ReportsTab = ({ 
  reports, 
  reportPermissions, 
  reportAccessRequests = {},
  onShowAccessRequests,
  onAttachmentClick,
  downloadingId,
  showPreview,
  previewUri,
  onClosePreview,
  showAccessRequestModal,
  onCloseAccessRequestModal,
  accessDuration,
  setAccessDuration,
  selectedReport,
  onApproveRequest,
  onRejectRequest,
  isLoading,
  onRefresh,
}) => {
  const { token } = useAuth();
  const [isUpdating, setIsUpdating] = useState(false);

  const handleApproveRequest = (requestId) => {
    onApproveRequest(requestId);
  };

  const handleRejectRequest = (requestId) => {
    onRejectRequest(requestId);
  };

  const renderReportMedia = (url) => {
    if (!url) return <View style={styles.evidenceImage} />;

    const transformedUrl = transformUrl(url);
    const isDocument = /\.(doc|docx|txt|xls|xlsx)$/i.test(url);
    const isPdf = /\.pdf$/i.test(url);

    if (isPdf) {
      return (
        <View style={styles.evidenceImage}>
          <Image
            source={require('../assets/images/pdf-icon.png')}
            style={styles.pdfThumbnail}
            resizeMode="cover"
          />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>PDF</Text>
          </View>
        </View>
      );
    } else if (isDocument) {
      return (
        <View style={styles.evidenceImage}>
          <MaterialCommunityIcons 
            name="file-document" 
            size={30} 
            color="#FFFFFF" 
            style={styles.playIcon} 
          />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>DOC</Text>
          </View>
        </View>
      );
    } else {
      return (
        <Image
          source={{ uri: transformedUrl }}
          style={styles.evidenceImage}
          defaultSource={require('../assets/images/small_satya_smadhanLogo.png')}
        />
      );
    }
  };

  const renderReportCard = ({ item: report }) => {
    if (!report) return null;
    
    const labName = report.forensicRequestId?.labId?.name || 'Unknown Lab';
    const status = reportPermissions[report._id] || 'denied';
    const requests = reportAccessRequests[report._id] || [];
    const requestCount = requests.filter(req => req.access === 'requested').length;

    return (
      <View style={styles.reportCard}>
        <View style={styles.titleContainer}>
          <Text style={styles.reportTitle}>{report.title || 'Untitled Report'}</Text>
          <View style={styles.badgeContainer}>
            {requestCount > 0 && (
              <TouchableOpacity 
                style={styles.requestBadge}
                onPress={() => onShowAccessRequests(report)}
              >
                <MaterialCommunityIcons name="account-clock" size={14} color={Colors.background} />
                <Text style={styles.requestCount}>{requestCount}</Text>
              </TouchableOpacity>
            )}
            {status !== 'granted' && (
              <View style={[styles.statusBadge, status === 'pending' ? styles.pendingBadge : styles.deniedBadge]}>
                <Text style={styles.statusText}>{status === 'pending' ? 'Pending' : 'No Access'}</Text>
              </View>
            )}
          </View>
        </View>
        {report.description && (
          <Text style={styles.reportDescription} numberOfLines={2}>
            {report.description}
          </Text>
        )}
        <Text style={styles.labName}>
          Lab: {labName}
        </Text>
        <Text style={styles.reportDate}>
          {new Date(report.createdAt).toLocaleDateString()}
        </Text>
        <View style={styles.attachmentsContainer}>
          {report.attachmentUrl && report.attachmentUrl.length > 0 ? (
            report.attachmentUrl.map((url, idx) => (
              <View key={idx} style={styles.attachmentItem}>
                {url.toLowerCase().endsWith('.pdf') ? (
                  <>
                    <View style={styles.evidenceImage}>
                      <Image
                        source={require('../assets/images/pdf-icon.png')}
                        style={styles.pdfThumbnail}
                        resizeMode="cover"
                      />
                      <View style={styles.videoOverlay}>
                        <Text style={styles.videoLabel}>PDF</Text>
                      </View>
                    </View>
                    <TouchableOpacity
                      style={styles.pdfOpenButton}
                      onPress={() => onAttachmentClick(url, report.title)}
                      disabled={downloadingId === `${report.title}-${url}`}
                    >
                      {downloadingId === `${report.title}-${url}` ? (
                        <ActivityIndicator size="small" color="#0B36A1" />
                      ) : (
                        <MaterialCommunityIcons
                          name="open-in-new"
                          size={20}
                          color="#0B36A1"
                        />
                      )}
                    </TouchableOpacity>
                  </>
                ) : (
                  <>
                    {renderReportMedia(url)}
                    <TouchableOpacity
                      style={styles.shareButton}
                      onPress={() => onAttachmentClick(url, report.title)}
                      disabled={downloadingId === `${report.title}-${url}`}
                    >
                      {downloadingId === `${report.title}-${url}` ? (
                        <ActivityIndicator size="small" color="#0B36A1" />
                      ) : (
                        <MaterialCommunityIcons
                          name="file-download"
                          size={24}
                          color="#0B36A1"
                        />
                      )}
                    </TouchableOpacity>
                  </>
                )}
              </View>
            ))
          ) : (
            <Text style={styles.noEvidenceText}>No attachments found</Text>
          )}
        </View>
      </View>
    );
  };

  const renderAccessRequestModal = () => (
    <Modal
      visible={showAccessRequestModal}
      transparent={true}
      animationType="fade"
      onRequestClose={onCloseAccessRequestModal}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.accessRequestContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Access Requests</Text>
            <TouchableOpacity 
              style={{padding: 4}}
              onPress={onCloseAccessRequestModal}
            >
              <MaterialCommunityIcons name="close-circle" size={24} color={Colors.primary} />
            </TouchableOpacity>
          </View>
          
          {selectedReport && reportAccessRequests[selectedReport._id]?.length > 0 ? (
            <FlatList
              data={reportAccessRequests[selectedReport._id]}
              keyExtractor={(item) => item._id}
              contentContainerStyle={{paddingBottom: 10}}
              renderItem={({item: request}) => (
                <View style={styles.requestCard}>
                  <View style={styles.requestHeader}>
                    <View style={styles.requesterInfo}>
                      <View style={styles.avatarContainer}>
                        {request.userId.displayUrl ? (
                          <Image
                            source={{ uri: transformUrl(request.userId.displayUrl) }}
                            style={styles.avatarImage}
                            onError={() => {
                              request.userId.displayUrl = null;
                            }}
                          />
                        ) : (
                          <MaterialCommunityIcons name="account" size={18} color={Colors.primary} />
                        )}
                      </View>
                      <View style={styles.requesterDetails}>
                        <Text style={styles.requesterName} numberOfLines={1}>{request.userId.name}</Text>
                        <Text style={styles.requesterRole} numberOfLines={1}>
                          <MaterialCommunityIcons name="badge-account-horizontal" size={12} color={Colors.primary} />
                          {' '}{request.userId.policeProfile?.designation || 'Unknown Role'}
                        </Text>
                        <Text style={styles.requestTime} numberOfLines={1}>
                          <MaterialCommunityIcons name="clock-outline" size={12} color={Colors.lightText} />
                          {' '}{new Date(request.createdAt).toLocaleString()}
                        </Text>
                      </View>
                    </View>
                    <View style={[
                      styles.statusBadge,
                      request.access === 'granted' ? styles.approvedBadge :
                      request.access === 'rejected' ? styles.deniedBadge :
                      request.access === 'pending' ? styles.pendingBadge :
                      styles.requestedBadge
                    ]}>
                      <MaterialCommunityIcons 
                        name={
                          request.access === 'granted' ? "check-circle" :
                          request.access === 'rejected' ? "close-circle" :
                          request.access === 'pending' ? "clock-outline" :
                          "account-clock"
                        }
                        size={14} 
                        color={Colors.background} 
                        style={{marginRight: 4}}
                      />
                      <Text style={styles.statusText}>
                        {request.access.charAt(0).toUpperCase() + request.access.slice(1)}
                      </Text>
                    </View>
                  </View>
                  
                  {request.access === 'requested' && (
                    <View style={styles.actionContainer}>
                      <View style={styles.durationContainer}>
                        <MaterialCommunityIcons name="timer-outline" size={16} color={Colors.primary} style={{marginRight: 6}} />
                        <Text style={styles.durationLabel}>Access Duration (hours):</Text>
                        <TextInput
                          style={styles.durationInput}
                          value={accessDuration}
                          onChangeText={setAccessDuration}
                          keyboardType="number-pad"
                          maxLength={3}
                        />
                      </View>
                      <View style={styles.buttonContainer}>
                        <TouchableOpacity
                          style={[styles.actionButton, styles.rejectButton]}
                          onPress={() => handleRejectRequest(request._id)}
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <ActivityIndicator size="small" color={Colors.background} />
                          ) : (
                            <>
                              <MaterialCommunityIcons name="close-circle" size={14} color={Colors.background} />
                              <Text style={styles.buttonText}>Reject</Text>
                            </>
                          )}
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={[styles.actionButton, styles.approveButton]}
                          onPress={() => handleApproveRequest(request._id)}
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <ActivityIndicator size="small" color={Colors.background} />
                          ) : (
                            <>
                              <MaterialCommunityIcons name="check-circle" size={14} color={Colors.background} />
                              <Text style={styles.buttonText}>Approve</Text>
                            </>
                          )}
                        </TouchableOpacity>
                      </View>
                    </View>
                  )}
                </View>
              )}
            />
          ) : (
            <View style={{alignItems: 'center', marginTop: 20}}>
              <MaterialCommunityIcons name="inbox-outline" size={48} color={Colors.lightText} />
              <Text style={styles.noRequestsText}>No pending access requests</Text>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {/* Refresh Button */}
      <View style={styles.refreshContainer}>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={onRefresh}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color={Colors.primary} />
          ) : (
            <>
              <MaterialCommunityIcons name="refresh" size={20} color={Colors.primary} />
      
            </>
          )}
        </TouchableOpacity>
      </View>
      {/* Reports List */}
      {reports && reports.length > 0 ? (
        <FlatList
          data={reports}
          renderItem={renderReportCard}
          keyExtractor={(item) => item._id}
          contentContainerStyle={{ paddingBottom: 20 }}
        />
      ) : (
        <Text style={styles.noEvidenceText}>No reports found</Text>
      )}

      <Modal
        visible={showPreview}
        transparent={true}
        animationType="fade"
        onRequestClose={onClosePreview}
      >
        <PreviewComponent 
          uri={previewUri} 
          onClose={onClosePreview} 
        />
      </Modal>

      {renderAccessRequestModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  refreshContainer: {
    alignItems: 'flex-end',
    marginBottom: 8,
    marginRight: 8,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.primary,
    borderRadius: 8,
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  refreshButtonText: {
    color: Colors.primary,
    marginLeft: 6,
    fontWeight: 'bold',
    fontSize: 14,
  },
  reportCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    width: '100%',
    backgroundColor: Colors.background,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  badgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  requestBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0B36A1',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  requestCount: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '500',
    marginLeft: 4,
    fontFamily: 'Roboto',
  },
  reportTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: Colors.black,
    marginBottom: 4,
    fontFamily: 'Roboto_bold',
  },
  reportDescription: {
    fontSize: 12,
    color: Colors.lightText,
    marginBottom: 8,
    fontFamily: 'Roboto',
  },
  labName: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '500',
    marginTop: 4,
    fontFamily: 'Roboto',
  },
  reportDate: {
    fontSize: 12,
    color: Colors.lightText,
    marginTop: 4,
    fontFamily: 'Roboto',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 0,
  },
  pendingBadge: {
    backgroundColor: '#FFA500',
  },
  deniedBadge: {
    backgroundColor: Colors.error,
  },
  approvedBadge: {
    backgroundColor: '#367E18',
  },
  requestedBadge: {
    backgroundColor: '#0B36A1',
  },
  statusText: {
    color: Colors.background,
    fontSize: 11,
    fontWeight: '500',
    fontFamily: 'Roboto',
    textTransform: 'capitalize',
  },
  attachmentsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    marginTop: 20,
  },
  attachmentItem: {
    width: 110,
    marginRight: 15,
    marginBottom: 20,
    alignItems: 'flex-start',
    position: 'relative',
  },
  evidenceImage: {
    height: 80,
    width: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  pdfThumbnail: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  pdfOpenButton: {
    position: 'absolute',
    left: 59,
    bottom: 55,
    backgroundColor: '#FFFFFF',
    borderRadius: 50,
    padding: 2,
    elevation: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  videoLabel: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
    fontFamily: 'Roboto',
  },
  playIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -15,
    marginTop: -15,
    zIndex: 10,
  },
  shareButton: {
    padding: 4,
    justifyContent: 'center',
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  noEvidenceText: {
    textAlign: 'center',
    color: Colors.lightText,
    fontStyle: 'italic',
    marginTop: 20,
    marginBottom: 40,
    fontFamily: 'Roboto',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: 16,
  },
  accessRequestContainer: {
    backgroundColor: Colors.background,
    padding: 16,
    borderRadius: 16,
    width: '100%',
    maxHeight: '80%',
    elevation: 5,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border + '30',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary,
    fontFamily: 'Roboto_bold',
    textAlign: 'left',
    flex: 1,
    marginRight: 8,
  },
  requestCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    width: '100%',
    backgroundColor: Colors.background,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  requestHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  requesterInfo: {
    flexDirection: 'row',
    flex: 1,
    marginRight: 8,
  },
  requesterDetails: {
    flex: 1,
    marginRight: 8,
  },
  avatarContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    flexShrink: 0,
    overflow: 'hidden',
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 16,
  },
  requesterName: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.black,
    fontFamily: 'Roboto_bold',
    marginBottom: 2,
  },
  requesterRole: {
    fontSize: 12,
    color: Colors.primary,
    fontFamily: 'Roboto',
    marginBottom: 2,
  },
  requestTime: {
    fontSize: 11,
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
  actionContainer: {
    marginTop: 10,
    borderTopWidth: 1,
    borderTopColor: Colors.border + '50',
    paddingTop: 10,
  },
  durationContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: 12,
    backgroundColor: Colors.border + '20',
    padding: 8,
    borderRadius: 8,
  },
  durationLabel: {
    fontSize: 12,
    color: Colors.black,
    marginRight: 8,
    fontFamily: 'Roboto',
    flex: 1,
  },
  durationInput: {
    width: 50,
    height: 32,
    borderColor: Colors.primary,
    borderWidth: 1,
    borderRadius: 6,
    padding: 4,
    fontSize: 12,
    color: Colors.black,
    fontFamily: 'Roboto_bold',
    backgroundColor: Colors.background,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  actionButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 16,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  approveButton: {
    backgroundColor: '#367E18',
  },
  rejectButton: {
    backgroundColor: '#888888',
  },
  buttonText: {
    color: Colors.background,
    fontSize: 12,
    fontWeight: '500',
    fontFamily: 'Roboto_bold',
    marginLeft: 4,
  },
  noRequestsText: {
    textAlign: 'center',
    color: Colors.lightText,
    fontStyle: 'italic',
    marginTop: 30,
    marginBottom: 40,
    fontFamily: 'Roboto',
    fontSize: 16,
  },
});

export default ReportsTab; 