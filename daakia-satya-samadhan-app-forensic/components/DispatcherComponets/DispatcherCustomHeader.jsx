import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function DispatcherCustomHeader({ title, searchHandler, notificationHandler, searchIcon, notificationIcon }) {
  return (
    <View style={styles.headerContainer}>
      {/* Left Side: Title only (no drawer menu) */}
      <View style={styles.leftContainer}>
        <Text style={styles.title}>{title}</Text>
      </View>

      {/* Right Icons (Search + Notifications) */}
      <View style={styles.rightIcons}>
        {searchHandler && (
          <TouchableOpacity onPress={searchHandler}>
            {searchIcon || <Ionicons name="search-outline" size={22} color="#9e9e9e" />}
          </TouchableOpacity>
        )}
        {notificationHandler && (
          <TouchableOpacity onPress={notificationHandler}>
            {notificationIcon || <Ionicons name="notifications-outline" size={22} color="#9e9e9e" />}
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
    headerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 25,
      paddingVertical: 20,
      backgroundColor: '#fff',
      borderBottomWidth: 1, 
      borderBottomColor: '#9e9e9e',
    },
    leftContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#333',
    },
    rightIcons: {
      flexDirection: 'row',
      gap: 23, 
    },
});