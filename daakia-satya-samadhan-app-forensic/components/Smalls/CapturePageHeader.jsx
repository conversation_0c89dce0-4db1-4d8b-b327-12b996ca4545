import React, { memo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../../constants/colors';

const CapturePageHeader = memo(({ title, subtitle }) => {
  return (
    <View style={styles.headerSection}>
      <Text style={styles.headerTitle}>{title}</Text>
      <Text style={styles.headerSubtitle}>{subtitle}</Text>
    </View>
  );
});

CapturePageHeader.displayName = 'CapturePageHeader';

const styles = StyleSheet.create({
  headerSection: {
    padding: 20,
    paddingTop: 30,
    backgroundColor: Colors.background,
  },
  headerTitle: {
    fontSize: 24,
    fontFamily: 'Roboto_bold',
    color: Colors.black,
    textAlign: 'center',
    marginBottom: 10,
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 14,
    fontFamily: 'Roboto',
    color: Colors.textlight,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default CapturePageHeader;