import React, { useState } from 'react';
import { View,Text, TextInput, StyleSheet } from 'react-native';

const EditText = ({hint,value,onChange,editable,textInputStyles,containerStyles, ptextcolor}) => {

// Function to render the hint text with styled asterisk
const renderHintText = () => {
    // Check if the hint includes '*'
    const [edit, setedit] = useState(true);
    const index = hint.indexOf('*');
    if (index !== -1) {
      return (
        <Text style={styles.hintText}>
          {hint.substring(0, index)}
          <Text style={styles.redAsterisk}>{hint.substring(index, index + 1)}</Text>
          {hint.substring(index + 1)}
        </Text>
      );
    } else {
      return <Text style={styles.hintText}>{hint}</Text>;
    }
  };



  return (
    <View style={[styles.container,containerStyles]}>
        {/* {renderHintText()} */}
      <TextInput
        style={[styles.textInput,textInputStyles]}
        value={value}
        placeholder={hint}
        onChangeText={onChange}
        editable={ editable }
        placeholderTextColor= {ptextcolor || 'black'}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width:'100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop:25,
    flex: 1
  },
  textInput: {
    height: 40,
    borderColor: 'gray',
    borderRadius: 8,
    borderWidth: 1,
    width: '100%',
    paddingHorizontal: 8,
  },
  redAsterisk: {
    color: 'red',
    fontSize: 16,
    marginRight: 5,
  },
});

export default EditText;