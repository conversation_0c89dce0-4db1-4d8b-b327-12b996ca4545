import React from 'react';
import { View, Text, TextInput, StyleSheet } from 'react-native';

const TitleField = ({ title, onTitleChange }) => {
  return (
    <View style={styles.formField}>
      <Text style={styles.fieldLabel}>
        Title Here <Text style={styles.requiredAsterisk}>*</Text>
      </Text>
      <TextInput
        style={styles.input}
        placeholder="Enter the title..."
        value={title}
        onChangeText={onTitleChange}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  formField: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#333',
  },
  requiredAsterisk: {
    color: 'red',
  },
  input: {
    borderWidth: 1,
    borderColor: '#e5e5e5',
    borderRadius: 8,
    padding: 12,
    textAlignVertical: 'top',
  },
});

export default TitleField;