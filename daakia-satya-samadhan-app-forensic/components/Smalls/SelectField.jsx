import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { SelectList } from 'react-native-dropdown-select-list';

const SelectField = ({ label, data, onSelect, placeholder, searchPlaceholder, disabled, isLoading }) => {
  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      {isLoading ? (
        <ActivityIndicator size="small" color="#0000ff" />
      ) : (
        <SelectList
          setSelected={onSelect}
          data={data}
          save="key"
          placeholder={placeholder}
          searchPlaceholder={searchPlaceholder}
          boxStyles={styles.selectListBox}
          dropdownStyles={styles.selectListDropdown}
          disabled={disabled}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 5,
    color: '#333',
  },
  selectListBox: {
    height: 50,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 15,
    backgroundColor: '#fff',
  },
  selectListDropdown: {
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    marginTop: 5,
  },
});

export default SelectField;