import React, { useState } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  useWindowDimensions,
  Image,
  TouchableOpacity,
  Text,
  Modal,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import DescriptionBox from './DescriptionBox';
import PreviewComponent from '../Larges/PreviewComponent';


const PhotoGrid = ({ images, description }) => {
  const { width } = useWindowDimensions();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null); // Track the selected item
  const isTablet = width >= 768;
  const isWindows = width > 1068;

  const numColumns = width < 600 ? 2 : width < 900 ? 3 : 4;

  const styles = StyleSheet.create({
    grid: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    imageContainer: {
      flex: 1,
      margin: 5,
      aspectRatio: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#f0f0f0',
      borderRadius: 8,
    },
    image: {
      width: '100%',
      height: '100%',
      borderRadius: 8,
    },
    videoPlaceholder: {
      width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 8,
      backgroundColor: '#000',
    },
    collapseButton: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: isWindows ? 50 : isTablet ? 10 : 10,
      backgroundColor: '#fff',
      borderRadius: 8,
      marginBottom: 10,
    },
    sectionTitle: {
      color: '#0B36A1',
      fontWeight: '500',
      fontSize: 16,
    },
  });

  const isVideo = (url) => {
    return url.match(/\.(mp4|mov|avi|wmv|flv|mkv)$/i) !== null;
  };

  const handleItemPress = (item) => {
    setSelectedItem(item); // Set the selected item when pressed
  };

  const handleClosePreview = () => {
    setSelectedItem(null); // Close the preview
  };

  return (
    <View>
      <TouchableOpacity
        style={styles.collapseButton}
        onPress={() => setIsCollapsed(!isCollapsed)}
      >
        <Text style={styles.sectionTitle}>Premises Images</Text>
        <MaterialCommunityIcons
          name={isCollapsed ? 'chevron-down' : 'chevron-up'}
          size={24}
          color="#0B36A1"
        />
      </TouchableOpacity>

      {/* Photo Grid */}
      {!isCollapsed && (
        <View>
          <FlatList
            data={images}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.imageContainer}
                onPress={() => handleItemPress(item)} // Handle item press
              >
                {isVideo(item) ? (
                  <View style={styles.videoPlaceholder}>
                    <MaterialCommunityIcons name="play-circle-outline" size={50} color="#fff" />
                  </View>
                ) : (
                  <Image source={{ uri: item }} style={styles.image} />
                )}
              </TouchableOpacity>
            )}
            keyExtractor={(item, index) => index.toString()}
            numColumns={numColumns}
            contentContainerStyle={styles.grid}
          />
          {description && <DescriptionBox description={description} />}
        </View>
      )}

      {/* Preview Modal */}
      <Modal
        visible={!!selectedItem} // Show modal if selectedItem is not null
        transparent={true}
        animationType="slide"
        onRequestClose={handleClosePreview}
      >
        <PreviewComponent
          uri={selectedItem} // Pass the selected item to PreviewComponent
          onClose={handleClosePreview} // Pass the close handler
        />
      </Modal>
    </View>
  );
};

export default PhotoGrid;