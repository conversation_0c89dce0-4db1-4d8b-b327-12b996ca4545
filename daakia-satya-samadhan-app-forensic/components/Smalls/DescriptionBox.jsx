import React from 'react';
import { View, Text, StyleSheet, useWindowDimensions } from 'react-native';

const DescriptionBox = ({ description }) => {
      const { width } = useWindowDimensions();
      const isTablet = width >= 768;
      const isWindows = width > 1068;
      const styles = StyleSheet.create({
        container: {
          marginTop:20,
          marginBottom: 10,
          paddingHorizontal: isWindows ? 50 : isTablet ? 10 : 10,
      
        },
        label: {
          fontSize: 14,
          fontWeight: 'bold',
          color: '#333',
          marginBottom: 4,
          marginBottom:10,
        },
        descriptionBox: {
          backgroundColor: '#fff',
          padding: 12,
          borderRadius: 8,
          borderWidth: 1,
          borderColor: '#ccc',
          minHeight: 80,
  
        },
        descriptionText: {
          fontSize: 14,
          color: '#333',
       
        },
      });
  return (
    <View style={styles.container}>
      <Text style={styles.label}>
      Premise Description <Text style={{ color: 'red' }}>*</Text>
      </Text>
      <View style={styles.descriptionBox}>
        <Text style={styles.descriptionText}>{description}</Text>
      </View>
    </View>
  );
};



export default DescriptionBox;
