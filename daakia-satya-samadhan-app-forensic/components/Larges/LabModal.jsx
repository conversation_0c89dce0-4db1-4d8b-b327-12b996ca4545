import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, Modal, StyleSheet, Alert } from "react-native";
import Constants from 'expo-constants';

const BASE_URL = Constants.expoConfig?.extra?.baseUrl;

// Enhanced error logger
const logError = (context, error) => {
  const errorMessage = `ERROR [${context}]: ${error.message || error}`;
  
  // Log to console with formatting for better visibility
  console.log('\n' + '='.repeat(80));
  console.log(errorMessage);
  console.log('Stack:', error.stack || 'No stack trace available');
  
  // Additional context if available
  if (error.response) {
    console.log('Response data:', error.response.data);
    console.log('Response status:', error.response.status);
  }
  
  if (error.request) {
    console.log('Request:', error.request);
  }
  
  if (error.config) {
    console.log('Request URL:', error.config.url);
    console.log('Request method:', error.config.method);
  }
  
  console.log('='.repeat(80) + '\n');
  
  return errorMessage;
};

const LabSelectionModal = ({
  isVisible,
  onClose,
  onConfirmSelection,
}) => {
  const [labs, setLabs] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [selectedLab, setSelectedLab] = useState(null);
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [errors, setErrors] = useState({});
  
  // Control which modal is currently visible
  const [isLabModalVisible, setIsLabModalVisible] = useState(false);
  const [isDeptModalVisible, setIsDeptModalVisible] = useState(false);
  
  // When the parent component makes the modal visible, show the lab selection first
  useEffect(() => {
    if (isVisible) {
      setIsLabModalVisible(true);
      fetchLabs();
    } else {
      // Reset state when modal is completely closed
      setIsLabModalVisible(false);
      setIsDeptModalVisible(false);
    }
  }, [isVisible]);

  // Fetch labs from API
  const fetchLabs = () => {
    if (!BASE_URL) {
      const errorMsg = logError('Environment', 'BASE_URL is undefined. Check your environment configuration.');
      setErrors(prev => ({ ...prev, baseUrl: errorMsg }));
      return;
    }
    
    console.log(`Fetching labs from: ${BASE_URL}/api/forensic/lab`);
    
    fetch(`${BASE_URL}/api/forensic/lab`)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log('Labs data received:', JSON.stringify(data, null, 2));
        if (data.status === "success") {
          setLabs(data.data);
        } else {
          throw new Error(`API returned error: ${data.message || 'Unknown error'}`);
        }
      })
      .catch((error) => {
        const errorMsg = logError('Fetching Labs', error);
        setErrors(prev => ({ ...prev, labsFetch: errorMsg }));
      });
  };

  // Fetch departments for the selected lab
  const fetchDepartments = (labId) => {
    if (!labId) {
      const errorMsg = logError('Fetch Departments', 'No lab ID provided');
      setErrors(prev => ({ ...prev, departmentsFetch: errorMsg }));
      return;
    }
    
    console.log(`Fetching departments for lab ID ${labId} from: ${BASE_URL}/api/forensic/lab/${labId}/department`);
    
    fetch(`${BASE_URL}/api/forensic/lab/${labId}/department`)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log('Departments data received:', JSON.stringify(data, null, 2));
        if (data.status === "success") {
          setDepartments(data.data);
          // Open department modal after departments are loaded
          setIsDeptModalVisible(true);
        } else {
          throw new Error(`API returned error: ${data.message || 'Unknown error'}`);
        }
      })
      .catch((error) => {
        const errorMsg = logError('Fetching Departments', error);
        setErrors(prev => ({ ...prev, departmentsFetch: errorMsg }));
        // Even in case of error, show the department modal (will display error message)
        setIsDeptModalVisible(true);
      });
  };

  // Handle lab selection
  const handleLabSelect = (lab) => {
    console.log('Lab selected:', lab.name, 'ID:', lab._id);
    setSelectedLab(lab);
    setIsLabModalVisible(false); // Close lab modal
    fetchDepartments(lab._id); // Fetch departments and then open dept modal
  };

  // Handle department selection
  const handleDepartmentSelect = (department) => {
    console.log('Department selected:', department.name, 'ID:', department._id);
    setSelectedDepartment(department);
  };

  // Handle confirmation
  const handleConfirm = () => {
    if (!selectedLab || !selectedDepartment) {
      const errorMsg = logError('Confirmation', 'No lab or department selected');
      setErrors(prev => ({ ...prev, confirmation: errorMsg }));
      Alert.alert('Selection Error', 'Please select both a lab and a department');
      return;
    }
    
    console.log('Confirming selection - Lab:', selectedLab.name, 'Department:', selectedDepartment.name);
    onConfirmSelection(selectedLab._id, selectedDepartment._id); 
    closeAllModals();
  };
  
  // Close all modals and call parent's onClose
  const closeAllModals = () => {
    setIsLabModalVisible(false);
    setIsDeptModalVisible(false);
    setSelectedLab(null);
    setSelectedDepartment(null);
    onClose();
  };

  return (
    <View>
      {/* Lab Selection Modal */}
      <Modal visible={isLabModalVisible} animationType="fade" transparent={true}>
        <View style={styles.overlay}>
          <View style={styles.modalView}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Lab</Text>
            </View>

            {labs.length > 0 ? (
              labs.map((lab, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.option}
                  onPress={() => handleLabSelect(lab)}
                >
                  <Text style={styles.optionText}>{lab.name}</Text>
                </TouchableOpacity>
              ))
            ) : (
              <Text style={styles.noDataText}>
                {errors.labsFetch ? 'Error loading labs' : 'No labs available'}
              </Text>
            )}

            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[styles.button, styles.secondaryButton]}
                onPress={closeAllModals}
              >
                <Text style={[styles.buttonText, styles.secondaryButtonText]}>Close</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Department Selection Modal */}
      <Modal visible={isDeptModalVisible} animationType="fade" transparent={true}>
        <View style={styles.overlay}>
          <View style={styles.modalView}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                Select Department for {selectedLab ? selectedLab.name : ''}
              </Text>
            </View>

            {departments.length > 0 ? (
              departments.map((dept, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.option,
                    selectedDepartment && selectedDepartment._id === dept._id && styles.selectedOption
                  ]}
                  onPress={() => handleDepartmentSelect(dept)}
                >
                  <Text style={styles.optionText}>{dept.name}</Text>
                </TouchableOpacity>
              ))
            ) : (
              <Text style={styles.noDataText}>
                {errors.departmentsFetch ? 'Error loading departments' : 'No departments available'}
              </Text>
            )}

            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[styles.button, styles.backButton]}
                onPress={() => {
                  setIsDeptModalVisible(false);
                  setIsLabModalVisible(true);
                }}
              >
                <Text style={[styles.buttonText]}>Back to Labs</Text>
              </TouchableOpacity>
              <View style={styles.buttonDivider} />
              <TouchableOpacity
                style={[
                  styles.button, 
                  styles.primaryButton,
                  !selectedDepartment && styles.disabledButton
                ]}
                onPress={handleConfirm}
                disabled={!selectedDepartment}
              >
                <Text style={[styles.buttonText, styles.primaryButtonText]}>CONFIRM</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalView: {
    width: "80%",
    backgroundColor: "white",
    borderRadius: 10,
    padding: 20,
  },
  modalHeader: {
    marginBottom: 15,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  option: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#ccc",
  },
  selectedOption: {
    backgroundColor: "#e6f7ff",
  },
  optionText: {
    fontSize: 16,
  },
  noDataText: {
    padding: 20,
    textAlign: 'center',
    color: '#666',
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  button: {
    padding: 10,
    borderRadius: 5,
    flex: 1,
    alignItems: "center",
  },
  primaryButton: {
    backgroundColor: "#007BFF",
  },
  secondaryButton: {
    backgroundColor: "#6C757D",
  },
  backButton: {
    backgroundColor: "#5a6268",
  },
  disabledButton: {
    backgroundColor: "#cccccc",
  },
  buttonText: {
    color: "white",
    fontWeight: "bold",
  },
  buttonDivider: {
    width: 10,
  },
});

export default LabSelectionModal;