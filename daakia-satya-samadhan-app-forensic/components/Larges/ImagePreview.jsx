import React from 'react';
import { StyleSheet, View, Image, TouchableOpacity, Text } from 'react-native';
import { AntDesign } from '@expo/vector-icons';
import VideoPlayer from './VideoPlayer';



const ImagePreview = ({ uri, onClose, isVideo }) => {
  return (
    <View style={styles.container}>
      {isVideo ? (
        <View style={styles.videoContainer}>
          <VideoPlayer uri={uri} style={styles.video} />
        </View>
      ) : (
        <Image source={{ uri }} style={styles.image} resizeMode="contain" />
      )}
      
      <TouchableOpacity style={styles.closeButton} onPress={onClose}>
        <AntDesign name="close" size={24} color="white" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  videoContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  video: {
    width: '100%',
    height: '90%',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 20,
    padding: 10,
    zIndex: 10, // Ensure close button is above video player
  },
});

export default ImagePreview;