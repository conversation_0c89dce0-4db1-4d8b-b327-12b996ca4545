import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet, Image } from 'react-native';

const LoadingScreen = ({ loadingText, primaryColor, secondaryColor }) => {
  return (
    <View style={[styles.container]}>
      <Image
        source={require('../../assets/images/satya_samadhan_logo.png')} 
        style={styles.logo}
      />
      <Text style={[styles.text, { color: primaryColor }]}>{loadingText}</Text>
      <ActivityIndicator size="large" color={primaryColor} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    width: 220,
    height: 160,
    marginBottom: 20,
  },
  text: {
    fontSize: 18,
    marginBottom: 20,
  },
});

export default LoadingScreen;