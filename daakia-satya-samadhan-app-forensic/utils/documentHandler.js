import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as WebBrowser from 'expo-web-browser';
import * as IntentLauncher from 'expo-intent-launcher';
import { Platform } from 'react-native';

// Cache object to store downloaded file URIs
const fileCache = new Map();

export const openDocument = async (url, type = 'pdf', onProgress = null) => {
  try {
    const transformedUrl = url.replace("http:", "https:");
    const filename = transformedUrl.split('/').pop();
    const localUri = `${FileSystem.documentDirectory}${filename}`;

    // Check if file exists in cache
    if (fileCache.has(transformedUrl)) {
      const cachedUri = fileCache.get(transformedUrl);
      // Verify if the cached file still exists
      const fileInfo = await FileSystem.getInfoAsync(cachedUri);
      if (fileInfo.exists) {
        return openLocalFile(cachedUri, type);
      }
      // If file doesn't exist, remove from cache
      fileCache.delete(transformedUrl);
    }

    // Check if file already exists in document directory
    const fileInfo = await FileSystem.getInfoAsync(localUri);
    if (fileInfo.exists) {
      fileCache.set(transformedUrl, localUri);
      return openLocalFile(localUri, type);
    }

    // Download file if it doesn't exist
    const downloadResumable = FileSystem.createDownloadResumable(
      transformedUrl,
      localUri,
      {},
      (downloadProgress) => {
        const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
        onProgress && onProgress(progress);
      }
    );

    const { uri } = await downloadResumable.downloadAsync();
    fileCache.set(transformedUrl, uri);
    return openLocalFile(uri, type);

  } catch (error) {
    console.error('Error handling document:', error);
    throw error;
  }
};

const openLocalFile = async (uri, type) => {
  try {
    if (Platform.OS === 'ios') {
      const canShare = await Sharing.isAvailableAsync();
      if (canShare) {
        await Sharing.shareAsync(uri);
      } else {
        await WebBrowser.openBrowserAsync(uri);
      }
    } else if (Platform.OS === 'android') {
      const contentUri = await FileSystem.getContentUriAsync(uri);
      await IntentLauncher.startActivityAsync('android.intent.action.VIEW', {
        data: contentUri,
        flags: 1,
        type: getMimeType(type),
      });
    }
    return true;
  } catch (error) {
    console.error('Error opening local file:', error);
    throw error;
  }
};

const getMimeType = (type) => {
  switch (type.toLowerCase()) {
    case 'pdf':
      return 'application/pdf';
    case 'image':
      return 'image/*';
    case 'video':
      return 'video/*';
    default:
      return 'application/octet-stream';
  }
};

// Optional: Function to clear cache
export const clearDocumentCache = async () => {
  try {
    for (const uri of fileCache.values()) {
      await FileSystem.deleteAsync(uri, { idempotent: true });
    }
    fileCache.clear();
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
};

// Add these functions if you want to manage cache
export const getCacheSize = async () => {
  let totalSize = 0;
  for (const uri of fileCache.values()) {
    const fileInfo = await FileSystem.getInfoAsync(uri);
    if (fileInfo.exists) {
      totalSize += fileInfo.size;
    }
  }
  return totalSize;
};

export const clearOldCache = async (maxAgeInDays = 7) => {
  const now = new Date();
  for (const [url, uri] of fileCache.entries()) {
    const fileInfo = await FileSystem.getInfoAsync(uri);
    if (fileInfo.exists) {
      const modificationTime = new Date(fileInfo.modificationTime);
      const ageInDays = (now - modificationTime) / (1000 * 60 * 60 * 24);
      if (ageInDays > maxAgeInDays) {
        await FileSystem.deleteAsync(uri, { idempotent: true });
        fileCache.delete(url);
      }
    }
  }
}; 