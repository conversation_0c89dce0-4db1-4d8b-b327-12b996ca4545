import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  ScrollView, 
  Alert, 
  Dimensions, 
  Platform,
  KeyboardAvoidingView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import InputField from '../../components/Smalls/InputField';
import SelectField from '../../components/Smalls/SelectField';
import ImageUploader from '../../components/Larges/ImageUploader';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useAuth } from '../../context/auth-context';
import { validationService } from '../../services/validation';
import { apiService } from '../../services/api';
import { AUTH_ERRORS } from '../../constants/auth';

const { width } = Dimensions.get('window');
const isTablet = width >= 768;
const isWeb = Platform.OS === 'web';

const responsiveSize = (size) => {
  if (isWeb) return size * 0.8;
  if (isTablet) return size * 1.2;
  return size;
};

export default function ForensicRegisterForm() {
  const { token } = useAuth();
  const [name, setName] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [designation, setDesignation] = useState('LabOfficer');
  const [lab, setLab] = useState('');
  const [aadhar, setAadhar] = useState('');
  const [emailId, setEmailId] = useState('');
  const [labDepartment, setLabDepartment] = useState('');
  const [labs, setLabs] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingDepartments, setIsFetchingDepartments] = useState(false);
  const [uploadedUrl, setUploadedUrl] = useState(null);
  const [designationOptions] = useState([
    { key: 'LabOfficer', value: 'Lab Officer' },
    { key: 'LabAssistant', value: 'Lab Assistant' },
    { key: 'LabTechnician', value: 'Lab Technician' }
  ]);

  const handleUploadComplete = (url) => {
    setUploadedUrl(url);
    console.log('Uploaded URL:', url);
  };

  useEffect(() => {
    fetchLabs();
  }, []);

  const fetchLabs = async () => {
    try {
      const response = await apiService.fetchLabs(token);
      if (response.status === 'success') {
        const labOptions = response.data.map(lab => ({
          key: lab._id,
          value: lab.name
        }));
        setLabs(labOptions);
      } else {
        throw new Error(AUTH_ERRORS.FETCH_LABS_FAILED);
      }
    } catch (error) {
      Alert.alert('Error', error.message || AUTH_ERRORS.FETCH_LABS_FAILED);
    }
  };

  const fetchDepartments = async (labId) => {
    if (!labId) return;
    setIsFetchingDepartments(true);
    try {
      const response = await apiService.fetchDepartments(token, labId);
      if (response.status === 'success') {
        const departmentOptions = response.data.map(dept => ({
          key: dept._id,
          value: dept.name
        }));
        setDepartments(departmentOptions);
      } else {
        throw new Error(AUTH_ERRORS.FETCH_DEPARTMENTS_FAILED);
      }
    } catch (error) {
      Alert.alert('Error', error.message || AUTH_ERRORS.FETCH_DEPARTMENTS_FAILED);
    } finally {
      setIsFetchingDepartments(false);
    }
  };

  const handleSignUp = async () => {
    try {
      const registrationData = {
        name,
        mobileNumber,
        designation,
        lab,
        aadhar,
        emailId,
        labDepartment,
        displayUrl: uploadedUrl,
      };

      console.log('Registration data:', registrationData);
      validationService.validateRegistrationData(registrationData);
      setIsLoading(true);

      const response = await apiService.register(token, registrationData);
      if (response.status === 'success') {
        Alert.alert('Success', 'Registration successful!', [
          {
            text: 'OK',
            onPress: () => router.replace('/(auth)/thankYouPage'),
          },
        ]);
      } else {
        throw new Error(response.message || AUTH_ERRORS.REGISTRATION_FAILED);
      }
    } catch (error) {
      Alert.alert('Error', error.message || AUTH_ERRORS.REGISTRATION_FAILED);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <Text style={styles.header}>FORENSIC REGISTRATION</Text>
          <Text style={styles.loginText}>
            Already have an account?{' '}
            <Text style={styles.loginLink} onPress={() => router.replace('/(auth)/login')}>
              LOG IN
            </Text>
          </Text>

          <View style={styles.formContainer}>
            <InputField
              label="Officer Name *"
              placeholder="Enter Officer Name"
              value={name}
              onChangeText={setName}
            />

            <SelectField
              label="Select Lab *"
              data={labs}
              onSelect={(val) => {
                setLab(val);
                fetchDepartments(val);
              }}
              placeholder="Select Lab"
              searchPlaceholder="Search Labs"
            />

            <SelectField
              label="Select Department *"
              data={departments}
              onSelect={(val) => setLabDepartment(val)}
              placeholder="Select Department"
              searchPlaceholder="Search Departments"
              disabled={!lab}
              isLoading={isFetchingDepartments}
            />

            <SelectField
              label="Designation *"
              data={designationOptions}
              onSelect={(val) => setDesignation(val)}
              placeholder="Select Designation"
              searchPlaceholder="Search Designations"
            />

            <InputField
              label="Aadhar No. *"
              placeholder="Enter Aadhar No."
              value={aadhar}
              onChangeText={setAadhar}
              keyboardType="numeric"
              maxLength={12}
            />

            <InputField
              label="Mobile No. *"
              placeholder="Enter Mobile No."
              value={mobileNumber}
              onChangeText={setMobileNumber}
              keyboardType="numeric"
              maxLength={10}
              prefix="+91"
            />

            <InputField
              label="Govt. Email ID *"
              placeholder="Enter Govt. Email ID"
              value={emailId}
              onChangeText={setEmailId}
              keyboardType="email-address"
            />

            <ImageUploader onUploadComplete={handleUploadComplete} />

            <TouchableOpacity
              style={[styles.signUpButton, isLoading && styles.disabledButton]}
              onPress={handleSignUp}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.signUpButtonText}>Register</Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: responsiveSize(20),
    paddingVertical: responsiveSize(10),
  },
  formContainer: {
    width: '100%',
  },
  header: {
    fontSize: responsiveSize(28),
    fontWeight: 'bold',
    marginBottom: responsiveSize(10),
    color: '#0647a1',
    textAlign: 'center',
  },
  loginText: {
    fontSize: responsiveSize(16),
    marginBottom: responsiveSize(20),
    textAlign: 'center',
    color: '#666',
  },
  loginLink: {
    color: '#0647a1',
    fontWeight: 'bold',
  },
  signUpButton: {
    backgroundColor: '#0647a1',
    padding: responsiveSize(15),
    borderRadius: 10,
    alignItems: 'center',
    marginTop: responsiveSize(20),
    width: isWeb ? '50%' : '100%',
    alignSelf: 'center',
  },
  disabledButton: {
    backgroundColor: '#a9a9a9',
  },
  signUpButtonText: {
    color: 'white',
    fontSize: responsiveSize(18),
    fontWeight: 'bold',
  },
});