import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Dimensions, Platform, StatusBar } from 'react-native';
import { useRouter } from 'expo-router';
import * as SecureStore from 'expo-secure-store';
import Loader from '../../components/Larges/loader';

const { width } = Dimensions.get('window');

export default function AuthLoadingScreen() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);


  const allowedRoles = ['officer', 'dispatcher']; 
  const allowedCategory = 'police'; 

  useEffect(() => {
    const checkAuth = async () => {
      const startTime = Date.now();
      try {
        const token = await SecureStore.getItemAsync('token');
        const roleStr = await SecureStore.getItemAsync('role');
        const category = await SecureStore.getItemAsync('category');
        const role = roleStr ? JSON.parse(roleStr) : [];
        if (!token || !role || !category) {
          throw new Error('Missing token, role, or category');
        }


        const isRoleAllowed = role.some((r) => allowedRoles.includes(r));
        const isCategoryAllowed = category === allowedCategory;

        if (!isRoleAllowed || !isCategoryAllowed) {
          const MIN_LOADING_TIME = 2000;
          const elapsedTime = Date.now() - startTime;
          const remainingTime = Math.max(MIN_LOADING_TIME - elapsedTime, 0);

          setTimeout(() => {
            router.replace('/(auth)/notAuthorizedScreen');
            setIsLoading(false);
          }, remainingTime);
          return; 
        }

    
        let target = '';
        if (role.includes('officer')) {
          target = '/(officer)'; 
        } else if (role.includes('dispatcher')) {
          target = '/(dispatcher)';
        } else {
          throw new Error('Invalid role');
        }

       
        const MIN_LOADING_TIME = 2000;
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(MIN_LOADING_TIME - elapsedTime, 0);

        setTimeout(() => {
     
          router.replace(target);
          setIsLoading(false);
        }, remainingTime);
      } catch (error) {
        console.error('Error during auth check:', error);

      
        const MIN_LOADING_TIME = 2000;
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(MIN_LOADING_TIME - elapsedTime, 0);

        setTimeout(() => {
          router.replace('/(auth)/welcome'); 
          setIsLoading(false);
        }, remainingTime);
      }
    };

    checkAuth();
  }, [router]);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      <Loader prompt="Authenticating..." />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
  },
});