import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Image, 
  FlatList,
  StatusBar,
  SafeAreaView,
  useWindowDimensions,
  TouchableOpacity,
  Modal,
  Platform,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as WebBrowser from 'expo-web-browser';
import * as IntentLauncher from 'expo-intent-launcher';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import PreviewComponent from '../../../components/Larges/PreviewComponent';
import SuccessScreen from '../../../components/Smalls/SuccessScreen';
import { openDocument } from '../../../utils/documentHandler';
import { Colors } from '../../../constants/colors';
import { transformUrl } from '../../../utils/transformUrl';



const isImageUrl = (url) => {
  if (!url) return false;
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
  return imageExtensions.some(ext => url.toLowerCase().endsWith(ext));
};

const isPdfUrl = (url) => {
  if (!url) return false;
  return url.toLowerCase().endsWith('.pdf');
};

const isVideoUrl = (url) => {
  if (!url) return false;
  const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv'];
  return videoExtensions.some(ext => url.toLowerCase().endsWith(ext));
};

const ViewAllDocuments = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { width } = useWindowDimensions();
  const [documents, setDocuments] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    // Process the attachmentUrl data from params
    if (params.attachmentUrl) {
      try {
        let attachmentUrls;
        
        // Handle different types of attachmentUrl
        if (Array.isArray(params.attachmentUrl)) {
          // It's already an array
          attachmentUrls = params.attachmentUrl;
        } else if (typeof params.attachmentUrl === 'string') {
          // Check if it looks like JSON
          if (params.attachmentUrl.trim().startsWith('[')) {
            // Attempt to parse JSON
            attachmentUrls = JSON.parse(params.attachmentUrl);
          } else {
            // It's a single URL string
            attachmentUrls = [params.attachmentUrl];
          }
        } else {
          attachmentUrls = [];
        }
        
        // Create document objects from the attachmentUrls
        const docs = Array.isArray(attachmentUrls) ? attachmentUrls.map((url, index) => ({
          id: `doc-${index}`,
          url: url,
          title: params.title || "Untitled Document",
          description: params.description || "No description available",
          reportId: params.reportId || null
        })) : [];
        
        setDocuments(docs);
      } catch (error) {
        console.error('Error processing attachment URLs:', error);
        setDocuments([]);
      }
    }
  }, [params.attachmentUrl, params.title, params.description, params.reportId]);

  const getColumns = (screenWidth) => {
    if (screenWidth >= 1600) return 6;
    if (screenWidth >= 1300) return 5;
    if (screenWidth >= 1000) return 4;
    if (screenWidth >= 700) return 3;
    return 2; 
  };
  
  const numColumns = getColumns(width);
  const HORIZONTAL_PADDING = 12;
  const ITEM_MARGIN = 8;
  const itemWidth = (width - (HORIZONTAL_PADDING * 2) - (ITEM_MARGIN * 2 * numColumns)) / numColumns;

  const handleDocumentPress = async (document) => {
    const url = document.url;
    
    if (isPdfUrl(url)) {
      setIsLoading(true);
      try {
        await openDocument(url, 'pdf', (progress) => {
          console.log(`Download progress: ${progress * 100}%`);
        });
      } catch (error) {
        Alert.alert('Error', 'Could not open the PDF file');
      } finally {
        setIsLoading(false);
      }
    } else if (isImageUrl(url) || isVideoUrl(url)) {
      setSelectedDocument(document);
      setPreviewVisible(true);
    }
  };

  const closePreview = () => {
    setPreviewVisible(false);
    setSelectedDocument(null);
  };

  const handleSubmit = () => {
    // Show success screen
    setShowSuccess(true);
  };

  const handleSuccessComplete = () => {
    setShowSuccess(false);
    router.replace({
      pathname: '(screens)/caseDetails',
      params: { forensicRequestId: params.forensicRequestId }
    });
  };
  const renderDocumentItem = ({ item }) => {
    const isPdf = isPdfUrl(item.url);
    const isVideo = isVideoUrl(item.url);
    const transformedUrl = transformUrl(item.url);
    
    return (
      <TouchableOpacity 
        style={[styles.documentCard, { width: itemWidth }]}
        onPress={() => handleDocumentPress(item)}
        disabled={isLoading}
      >
        <View style={styles.imageContainer}>
          {isLoading ? (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size="large" color="#0a34a1" />
            </View>
          ) : isPdf ? (
            <View style={styles.pdfContainer}>
              <Ionicons name="document-text" size={48} color="#E53E3E" />
              <Text style={styles.pdfText}>PDF</Text>
            </View>
          ) : isVideo ? (
            <View style={styles.videoContainer}>
              <Ionicons name="videocam" size={48} color="#3182CE" />
              <Text style={styles.videoText}>Video</Text>
            </View>
          ) : transformedUrl ? (
            <Image
              source={{ uri: transformedUrl }}
              style={styles.documentImage}
              resizeMode="cover"
            />
          ) : (
            <View style={[styles.documentImage, styles.noImageContainer]}>
              <Text style={styles.noImageText}>No Image</Text>
            </View>
          )}
        </View>
        <View style={styles.documentInfo}>
          <Text style={styles.documentTitle} numberOfLines={1} ellipsizeMode="tail">
            {item.title}
          </Text>
          <Text style={styles.documentDescription} numberOfLines={2} ellipsizeMode="tail">
            {item.description}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" />
      <View style={styles.container}>
        {documents.length > 0 ? (
          <>
            <FlatList
              data={documents}
              renderItem={renderDocumentItem}
              keyExtractor={item => item.id}
              numColumns={numColumns}
              key={numColumns} // Needed to re-render when columns change
              contentContainerStyle={styles.listContent}
              showsVerticalScrollIndicator={false}
            />
            <View style={styles.buttonContainer}>
              <TouchableOpacity 
                style={styles.submitButton}
                onPress={handleSubmit}
              >
                <Text style={styles.submitButtonText}>Submit</Text>
              </TouchableOpacity>
            </View>
          </>
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="document-outline" size={64} color="#CBD5E0" />
            <Text style={styles.emptyText}>No attachments found</Text>
          </View>
        )}
        
        {/* Modal for image preview */}
        {previewVisible && selectedDocument && (
          <Modal
            visible={previewVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={closePreview}
          >
            <PreviewComponent
              uri={transformUrl(selectedDocument.url)}
              onClose={closePreview}
            />
          </Modal>
        )}

        {/* Success Screen */}
        {showSuccess && (
          <SuccessScreen
            message="Report Uploaded Successfully!"
            duration={2000}
            onComplete={handleSuccessComplete}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#0a34a1',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 12,
    fontFamily:'Roboto_Bold'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    fontFamily:'Roboto_Bold'
  },
  listContent: {
    padding: 12,
    paddingBottom: 80, // Add padding to make room for the button
  },
  documentCard: {
    margin: 8,
    borderRadius: 8,
    backgroundColor: '#f2f2f2',
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    height: 220, 
  },
  imageContainer: {
    height: 150, // Fixed height for image area
    backgroundColor: '#f8f9fa',
  },
  documentImage: {
    width: '100%',
    height: '100%',
  },
  pdfContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF5F5',
  },
  pdfText: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '500',
    color: '#E53E3E',
    fontFamily:'Roboto_Bold'
  },
  videoContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#EBF8FF',
  },
  videoText: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '500',
    color: '#3182CE',
  },
  noImageContainer: {
    backgroundColor: '#eeeeee',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noImageText: {
    color: '#757575',
    fontSize: 14,
  },
  documentInfo: {
    padding: 12,
    height: 70, // Fixed height for info area
    fontFamily:'Roboto'
  },
  documentTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  documentDescription: {
    fontSize: 12,
    color: '#666666',
    fontFamily:'Roboto'
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#718096',
    marginTop: 12,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
  },
  submitButton: {
    backgroundColor: Colors.primary,
    borderRadius: 50,
    paddingVertical: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
  },
  submitButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '600',
    fontWeight:'bold',
    fontFamily:'Roboto_Bold'
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    fontFamily:'Roboto_Bold'
  },
});

export default ViewAllDocuments;