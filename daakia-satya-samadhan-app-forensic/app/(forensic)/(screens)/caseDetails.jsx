import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  Alert,
  Modal,
  Platform,
  TextInput,
} from "react-native";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import Constants from 'expo-constants';
import * as FileSystem from 'expo-file-system';
import * as IntentLauncher from 'expo-intent-launcher';
import * as Sharing from 'expo-sharing';
import * as WebBrowser from 'expo-web-browser';
import { useAuth } from "../../../context/auth-context";
import { router, useLocalSearchParams } from "expo-router";
import { transformUrl } from "../../../utils/transformUrl";
import { Colors } from "../../../constants/colors";
import PreviewComponent from '../../../components/Larges/PreviewComponent';
import ReportsTab from '../../../components/ReportsTab';

const BASE_URL = Constants.expoConfig.extra.baseUrl;

/* ==============================================
   EVIDENCE ACCEPTANCE MODAL - KEPT FOR REFERENCE
   ============================================== */
/*
const EvidenceAcceptanceModal = ({ visible, onClose, onAccept, onReject }) => {
  const [rejectionReason, setRejectionReason] = useState('');

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.accessRequestContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Evidence Review</Text>
            <TouchableOpacity 
              style={{padding: 4}}
              onPress={onClose}
            >
              <MaterialCommunityIcons name="close-circle" size={24} color={Colors.primary} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.caseAcceptanceContent}>
            <MaterialCommunityIcons 
              name="clipboard-check-outline" 
              size={40} 
              color={Colors.primary} 
              style={{alignSelf: 'center', marginBottom: 12}} 
            />
            <Text style={styles.caseAcceptanceText}>
              Before creating a report for this evidence, please confirm if you accept it as valid evidence.
            </Text>
            
            <View style={styles.rejectionContainer}>
              <Text style={styles.rejectionLabel}>
                Reason for Rejection (required if rejecting):
              </Text>
              <TextInput 
                style={styles.rejectionInput}
                value={rejectionReason}
                onChangeText={setRejectionReason}
                placeholder="Enter reason for rejection"
                multiline={true}
                numberOfLines={3}
              />
            </View>
            
            <View style={styles.caseButtonContainer}>
              <TouchableOpacity
                style={[styles.actionButton, styles.rejectButton]}
                onPress={() => onReject(rejectionReason)}
              >
                <MaterialCommunityIcons name="close-circle" size={14} color={Colors.background} />
                <Text style={styles.buttonText}>Reject Evidence</Text>
              </TouchableOpacity>
              <View style={{width: 10}} />
              <TouchableOpacity
                style={[styles.actionButton, styles.approveButton]}
                onPress={onAccept}
              >
                <MaterialCommunityIcons name="check-circle" size={14} color={Colors.background} />
                <Text style={styles.buttonText}>Accept & Report</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};
*/

const ForensicRequestDetails = () => {
  const { token } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [requestData, setRequestData] = useState(null);
  const { forensicRequestId } = useLocalSearchParams();
  const [activeTab, setActiveTab] = useState('evidences');
  const [reports, setReports] = useState([]);
  const [reportPermissions, setReportPermissions] = useState({});
  const [showPreview, setShowPreview] = useState(false);
  const [previewUri, setPreviewUri] = useState(null);
  const [downloadingId, setDownloadingId] = useState(null);
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [accessDuration, setAccessDuration] = useState('24');
  const [showAccessRequestModal, setShowAccessRequestModal] = useState(false);
  const [reportAccessRequests, setReportAccessRequests] = useState({});

  useEffect(() => {
    fetchForensicRequestDetails();
  }, [forensicRequestId]);

  useEffect(() => {
    if (requestData?.caseId?._id) {
      fetchReports();
    }
  }, [requestData]);

  const fetchAccessRequests = async (reportId) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/forensic/report/access/${reportId}`,
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      
      const result = await response.json();
      if (result.status === 'success') {
        // Group requests by userId and keep only the latest one
        const uniqueRequests = result.data.reduce((acc, request) => {
          const userId = request.userId._id;
          
          // If this is the first request for this user or it's newer than existing one
          if (!acc[userId] || new Date(request.createdAt) > new Date(acc[userId].createdAt)) {
            acc[userId] = request;
          }
          return acc;
        }, {});

        // Convert back to array
        const filteredRequests = Object.values(uniqueRequests);

        // Update the access requests state
        setReportAccessRequests(prev => ({
          ...prev,
          [reportId]: filteredRequests
        }));

        const formattedData = {
          reportId,
          totalRequests: filteredRequests.length,
          requests: filteredRequests.map(req => ({
            id: req._id,
            access: req.access,
            createdAt: req.createdAt,
            userId: req.userId
          }))
        };
      }
      return result;
    } catch (err) {
      return null;
    }
  };

  const fetchReports = async () => {
    if (!requestData?.caseId?._id) return;
    
    try {
      const response = await fetch(
        `${BASE_URL}/api/forensic/report/case/${requestData.caseId._id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      
      const result = await response.json();
      
      if (result.status === 'success') {
        const processedReports = (result.data || []).map(report => {
          if (!report.evidenceId && report.evidenceDetails) {
            report.evidenceId = report.evidenceDetails._id;
          }
          
          if (!report.evidenceId && report.url && report.url.includes('evidence/')) {
            const matches = report.url.match(/evidence\/([^\/]+)/);
            if (matches && matches[1]) {
              report.evidenceId = matches[1];
            }
          }
          
          return report;
        });
        
        setReports(processedReports);
        
        const initialPermissions = {};
        processedReports.forEach(report => {
          initialPermissions[report._id] = 'granted';
        });
        setReportPermissions(initialPermissions);

        // Fetch access requests for each report
        processedReports.forEach(report => {
          fetchAccessRequests(report._id);
        });
      } else {
      }
    } catch (err) {
    }
  };

  const fetchForensicRequestDetails = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `${BASE_URL}/api/forensic/request/${forensicRequestId}/evidences`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      
      const result = await response.json();
      
      if (result.status === 'success') {
        setRequestData(result.data);
      } else {
        setError(result.message || 'Failed to fetch request details');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSharePress = (evidenceId) => {
    router.replace({
      pathname: '(screens)/captureReport',
      params: { 
        evidenceId,
        caseId: requestData?.caseId?._id,
        forensicRequestId
      }
    });
  };

  const handleReportClick = () => {
    setActiveTab('reports');
    fetchReports();
  };

  const handleEvidenceClick = () => {
    setActiveTab('evidences');
  };

  const handleAttachmentClick = (url, title) => {
    if (!url) {
      Alert.alert('Error', 'No file URL provided');
      return;
    }

    const isPdf = url.toLowerCase().endsWith('.pdf');
    const isImageFile = /\.(jpg|jpeg|png|gif|webp)$/i.test(url);

    if (isPdf) {
      downloadAndOpenPdf(url, title);
    } else if (isImageFile) {
      setPreviewUri(transformUrl(url));
      setShowPreview(true);
    } else {
      const transformedUrl = transformUrl(url);
      WebBrowser.openBrowserAsync(transformedUrl);
    }
  };

  const closePreview = () => {
    setShowPreview(false);
    setPreviewUri(null);
  };

  const handleShowAccessRequests = (report) => {
    setSelectedReport(report);
    setShowAccessRequestModal(true);
  };

  const handleEvidencePress = (evidenceId) => {
    router.push({
      pathname: '(screens)/evidancesDetails',
      params: { 
        evidenceId,
        caseId: requestData?.caseId?._id,
        forensicRequestId
      }
    });
  };

  // Functions for handling report attachments
  const downloadAndOpenPdf = async (url, title) => {
    if (!url) {
      Alert.alert('Error', 'No file URL provided');
      return;
    }
    
    try {
      setDownloadingId(`${title}-${url}`); // Unique ID for each URL
      const transformedUrl = transformUrl(url);
      const filename = url.split('/').pop();
      const localUri = `${FileSystem.documentDirectory}${filename}`;
      
      const downloadResumable = FileSystem.createDownloadResumable(
        transformedUrl,
        localUri,
        {},
        (downloadProgress) => {
          const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
        }
      );
      
      const { uri } = await downloadResumable.downloadAsync();
      
      if (Platform.OS === 'ios') {
        const canShare = await Sharing.isAvailableAsync();
        if (canShare) {
          await Sharing.shareAsync(uri);
        } else {
          await WebBrowser.openBrowserAsync(transformedUrl);
        }
      } else if (Platform.OS === 'android') {
        const contentUri = await FileSystem.getContentUriAsync(uri);
        await IntentLauncher.startActivityAsync('android.intent.action.VIEW', {
          data: contentUri,
          flags: 1,
          type: 'application/pdf',
        });
      }
    } catch (error) {
      Alert.alert('Error', 'Could not download or open the PDF file');
    } finally {
      setDownloadingId(null);
    }
  };

  const renderEvidenceMedia = (url) => {
    if (!url) return <View style={styles.evidenceImage} />;

    // Use the imported transformUrl utility
    const transformedUrl = transformUrl(url);
    
    const isVideo = /\.(mp4|mov|avi|wmv|flv|webm|mkv)$/i.test(url);

    if (isVideo) {
      return (
        <View style={styles.evidenceImage}>
          <MaterialCommunityIcons name="play-circle" size={30} color="#FFFFFF" style={styles.playIcon} />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>VIDEO</Text>
          </View>
        </View>
      );
    } else {
      return (
        <Image
          source={{ uri: transformedUrl }}
          style={styles.evidenceImage}
          defaultSource={require('../../../assets/images/small_satya_smadhanLogo.png')}
        />
      );
    }
  };

  const renderReportMedia = (url) => {
    if (!url) return <View style={styles.evidenceImage} />;

    const transformedUrl = transformUrl(url);
    const isDocument = /\.(doc|docx|txt|xls|xlsx)$/i.test(url);
    const isPdf = /\.pdf$/i.test(url);

    if (isPdf) {
      return (
        <View style={styles.evidenceImage}>
          <Image
            source={require('../../../assets/images/pdf-icon.png')}
            style={styles.pdfThumbnail}
            resizeMode="cover"
          />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>PDF</Text>
          </View>
        </View>
      );
    } else if (isDocument) {
      return (
        <View style={styles.evidenceImage}>
          <MaterialCommunityIcons 
            name="file-document" 
            size={30} 
            color="#FFFFFF" 
            style={styles.playIcon} 
          />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>DOC</Text>
          </View>
        </View>
      );
    } else {
      return (
        <Image
          source={{ uri: transformedUrl }}
          style={styles.evidenceImage}
          defaultSource={require('../../../assets/images/small_satya_smadhanLogo.png')}
        />
      );
    }
  };

  const handleApproveRequest = async (requestId) => {
    try {
      setIsLoading(true);
      
      // Calculate end date based on duration (in hours)
      const endDate = new Date();
      endDate.setHours(endDate.getHours() + parseInt(accessDuration));

      const response = await fetch(`${BASE_URL}/api/forensic/report/access`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          access: 'granted',
          reportAccessId: requestId,
          duration: endDate.toISOString(),
        }),
      });

      const result = await response.json();

      if (result.status === 'success' && result.data) {
        // Update the local state with the new access status
        if (selectedReport && reportAccessRequests[selectedReport._id]) {
          const updatedRequests = reportAccessRequests[selectedReport._id].map(req => {
            if (req._id === requestId) {
              return {
                ...req,
                access: result.data.access,
                duration: result.data.duration,
                updatedAt: result.data.updatedAt
              };
            }
            return req;
          });
          
          setReportAccessRequests(prev => ({
            ...prev,
            [selectedReport._id]: updatedRequests
          }));
        }
      }
    } catch (error) {
      // Handle error silently
    } finally {
      setIsLoading(false);
    }
  };

  const handleRejectRequest = async (requestId) => {
    try {
      setIsLoading(true);

      const response = await fetch(`${BASE_URL}/api/forensic/report/access`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          access: 'rejected',
          reportAccessId: requestId,
          duration: null,
        }),
      });

      const result = await response.json();

      if (result.status === 'success' && result.data) {
        // Update the local state with the new access status
        if (selectedReport && reportAccessRequests[selectedReport._id]) {
          const updatedRequests = reportAccessRequests[selectedReport._id].map(req => {
            if (req._id === requestId) {
              return {
                ...req,
                access: result.data.access,
                updatedAt: result.data.updatedAt
              };
            }
            return req;
          });
          
          setReportAccessRequests(prev => ({
            ...prev,
            [selectedReport._id]: updatedRequests
          }));
        }
      }
    } catch (error) {
      // Handle error silently
    } finally {
      setIsLoading(false);
    }
  };

  const renderItem = ({ item }) => {
    switch (item.type) {
      case 'caseDetails':
        return (
          <View style={styles.detailsContainer}>
            {[
              { label: 'Created At', value: new Date(requestData.caseId.createdAt).toLocaleString() },
              { label: 'Title', value: requestData.caseId.title },
              { label: 'Description', value: requestData.caseId.description },
              { label: 'Case Type', value: requestData.caseId.caseType },
              { label: 'Remarks', value: requestData.caseId.remarks },
              { label: 'Police Station', value: requestData.policeStationId?.name || 'Not Assigned' },
            ].map((detail, index) => (
              <View style={styles.detailRow} key={index}>
                <Text style={styles.label}>{detail.label}</Text>
                <Text style={styles.value}>{detail.value}</Text>
              </View>
            ))}
          </View>
        );
      case 'evidences':
        return (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Evidences</Text>
            
            <View style={styles.segmentedControlContainer}>
              <View style={styles.segmentedControl}>
                <TouchableOpacity
                  style={[styles.segmentButton, activeTab === 'evidences' && styles.activeSegment]}
                  onPress={handleEvidenceClick}
                >
                  <MaterialCommunityIcons name="file-document-multiple-outline" size={18} color={activeTab === 'evidences' ? Colors.background : Colors.lightText} />
                  <Text style={activeTab === 'evidences' ? styles.activeSegmentText : styles.segmentText}>Evidences</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.segmentButton, activeTab === 'reports' && styles.activeSegment]}
                  onPress={handleReportClick}
                >
                  <MaterialCommunityIcons name="clipboard-text-outline" size={18} color={activeTab === 'reports' ? Colors.background : Colors.lightText} />
                  <Text style={activeTab === 'reports' ? styles.activeSegmentText : styles.segmentText}>Reports</Text>
                </TouchableOpacity>
              </View>
            </View>
            
            {activeTab === 'evidences' ? (
              requestData.evidences && requestData.evidences.length > 0 ? (
                requestData.evidences.map((evidence, index) => {
                  if (!evidence) return null;
                  
                  const firstAttachment = evidence.attachmentUrl?.[0] || null;

                  return (
                    <TouchableOpacity
                      key={evidence._id || index}
                      style={styles.evidenceCard}
                      onPress={() => handleEvidencePress(evidence._id)}
                    >
                      <View style={styles.imageContainer}>
                        {renderEvidenceMedia(firstAttachment)}
                      </View>
                      <View style={styles.evidenceDetails}>
                        <Text style={styles.evidenceTitle}>{evidence.title || 'Untitled Evidence'}</Text>
                        <Text style={styles.evidenceLabel}>
                          Type: <Text style={styles.evidenceType}>{evidence.type || 'Unknown'}</Text>
                        </Text>

                        {evidence.description && (
                          <Text style={styles.evidenceLabel}>
                            Description: <Text style={styles.evidenceType}>{evidence.description}</Text>
                          </Text>
                        )}
                        
                        {evidence.attachmentUrl && evidence.attachmentUrl.length > 1 && (
                          <Text style={styles.evidenceLabel}>
                            Attachments: <Text style={styles.evidenceType}>{evidence.attachmentUrl.length}</Text>
                          </Text>
                        )}
                      </View>
                      <TouchableOpacity
                        style={styles.shareButton}
                        onPress={() => handleSharePress(evidence._id)}
                      >
                        <MaterialCommunityIcons
                          name="share-circle"
                          size={24}
                          color="#0B36A1"
                        />
                      </TouchableOpacity>
                    </TouchableOpacity>
                  );
                })
              ) : (
                <Text style={styles.noEvidenceText}>No evidences found</Text>
              )
            ) : (
              <ReportsTab
                reports={reports}
                reportPermissions={reportPermissions}
                reportAccessRequests={reportAccessRequests}
                onShowAccessRequests={handleShowAccessRequests}
                onAttachmentClick={handleAttachmentClick}
                downloadingId={downloadingId}
                showPreview={showPreview}
                previewUri={previewUri}
                onClosePreview={closePreview}
                showAccessRequestModal={showAccessRequestModal}
                onCloseAccessRequestModal={() => setShowAccessRequestModal(false)}
                accessDuration={accessDuration}
                setAccessDuration={setAccessDuration}
                selectedReport={selectedReport}
                onApproveRequest={handleApproveRequest}
                onRejectRequest={handleRejectRequest}
                isLoading={isLoading}
                onRefresh={fetchReports}
              />
            )}
          </View>
        );
      default:
        return null;
    }
  };

  // Error display
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="alert-circle-outline" size={48} color="#FF3B30" />
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchForensicRequestDetails}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Full-screen loader
  if (isLoading) {
    return (
      <View style={styles.fullScreenLoader}>
        <ActivityIndicator size="large" color="#0B36A1" />
      </View>
    );
  }

  // Data sections for the FlatList
  const sections = requestData ? [
    { type: 'caseDetails' },
    { type: 'evidences' }
  ] : [];

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <FlatList
          data={sections}
          renderItem={renderItem}
          keyExtractor={(item, index) => `${item.type}-${index}`}
          contentContainerStyle={{ flexGrow: 1 }}
          initialNumToRender={2}
          maxToRenderPerBatch={2}
          windowSize={3}
        />
        <Modal
          visible={showPreview}
          transparent={true}
          animationType="fade"
          onRequestClose={closePreview}
        >
          <PreviewComponent 
            uri={previewUri} 
            onClose={closePreview} 
          />
        </Modal>
      </View>
    </GestureHandlerRootView>
  );
};

export default React.memo(ForensicRequestDetails);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  detailsContainer: {
    paddingHorizontal: '4%',
    paddingVertical: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  label: {
    fontSize: 14,
    color: Colors.lightText,
    width: '40%',
    fontFamily: 'Roboto_bold',
  },
  value: {
    fontSize: 14,
    color: Colors.black,
    width: '60%',
    fontFamily: 'Roboto',
  },
  sectionContainer: {
    marginTop: 12,
    marginBottom: 32,
    paddingHorizontal: '4%',
  },
  sectionTitle: {
    textAlign: 'left',
    color: Colors.primary,
    fontWeight: 'bold',
    fontFamily: 'Roboto_bold',
    fontSize: 20,
    marginBottom: 24,
    marginTop: 8,
  },
  evidenceCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginBottom: 16,
    padding: 12,
    borderRadius: 12,
    flexDirection: 'row',
    position: 'relative',
  },
  imageContainer: {
    width: 80,
    marginRight: 12,
  },
  evidenceImage: {
    height: 80,
    width: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  evidenceDetails: {
    flex: 1,
  },
  evidenceLabel: {
    fontSize: 12,
    color: Colors.lightText,
    marginBottom: 4,
    fontFamily: 'Roboto',
  },
  evidenceType: {
    fontSize: 12,
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
  evidenceTitle: {
    fontSize: 16,
    color: Colors.black,
    fontFamily: 'Roboto_bold',
    marginBottom: 4,
  },
  shareButton: {
    padding: 4,
    justifyContent: 'center',
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  videoLabel: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
    fontFamily: 'Roboto',
  },
  playIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -15,
    marginTop: -15,
    zIndex: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.background,
  },
  errorText: {
    color: Colors.error,
    marginVertical: 16,
    textAlign: 'center',
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontFamily: 'Roboto_bold',
  },
  fullScreenLoader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  noEvidenceText: {
    textAlign: 'center',
    color: Colors.lightText,
    fontStyle: 'italic',
    marginTop: 20,
    marginBottom: 40,
    fontFamily: 'Roboto',
  },
  segmentedControlContainer: {
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  segmentedControl: {
    flexDirection: 'row',
    borderRadius: 30,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: '#E8E8E8',
  },
  segmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    justifyContent: 'center',
    width: 150,
  },
  activeSegment: {
    backgroundColor: '#367E18',
    borderRadius: 30,
  },
  segmentText: {
    color: '#666666',
    marginLeft: 8,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  activeSegmentText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  reportCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    width: '100%',
    backgroundColor: Colors.background,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  badgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  requestBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0B36A1',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  requestCount: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '500',
    marginLeft: 4,
    fontFamily: 'Roboto',
  },
  reportTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: Colors.black,
    marginBottom: 4,
    fontFamily: 'Roboto_bold',
  },
  reportDescription: {
    fontSize: 12,
    color: Colors.lightText,
    marginBottom: 8,
    fontFamily: 'Roboto',
  },
  labName: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '500',
    marginTop: 4,
    fontFamily: 'Roboto',
  },
  reportDate: {
    fontSize: 12,
    color: Colors.lightText,
    marginTop: 4,
    fontFamily: 'Roboto',
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 30,
    marginLeft: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  pendingBadge: {
    backgroundColor: '#FFA500',
  },
  deniedBadge: {
    backgroundColor: Colors.error,
  },
  approvedBadge: {
    backgroundColor: '#367E18',
  },
  statusText: {
    color: Colors.background,
    fontSize: 12,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  attachmentsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    marginTop: 20,
  },
  attachmentItem: {
    width: 110,
    marginRight: 15,
    marginBottom: 20,
    alignItems: 'flex-start',
    position: 'relative',
  },
  pdfThumbnail: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  pdfOpenButton: {
    position: 'absolute',
    left:59,
    bottom: 55,
    backgroundColor: '#FFFFFF',
    borderRadius: 50,
    padding: 2,
    elevation: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  accessRequestContainer: {
    backgroundColor: Colors.background,
    padding: 20,
    borderRadius: 16,
    width: '90%',
    maxHeight: '80%',
    elevation: 5,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border + '30',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary,
    fontFamily: 'Roboto_bold',
    textAlign: 'left',
  },
  reportInfoText: {
    fontSize: 16,
    color: Colors.black,
    marginBottom: 20,
    fontFamily: 'Roboto_bold',
    textAlign: 'center',
  },
  requestCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginBottom: 12,
    padding: 12,
    borderRadius: 8,
    width: '100%',
    backgroundColor: Colors.background,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  requestHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  requesterName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.black,
    fontFamily: 'Roboto_bold',
    marginBottom: 4,
  },
  requesterRole: {
    fontSize: 13,
    color: Colors.primary,
    fontFamily: 'Roboto',
    marginBottom: 2,
  },
  requestTime: {
    fontSize: 12,
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
  actionContainer: {
    marginTop: 10,
    borderTopWidth: 1,
    borderTopColor: Colors.border + '50',
    paddingTop: 10,
  },
  durationContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: 12,
    backgroundColor: Colors.border + '20',
    padding: 8,
    borderRadius: 8,
  },
  durationLabel: {
    fontSize: 12,
    color: Colors.black,
    marginRight: 10,
    fontFamily: 'Roboto',
    flexShrink: 1,
  },
  durationInput: {
    width: 60,
    height: 36,
    borderColor: Colors.primary,
    borderWidth: 1,
    borderRadius: 6,
    padding: 6,
    fontSize: 14,
    color: Colors.black,
    fontFamily: 'Roboto_bold',
    backgroundColor: Colors.background,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 20,
    minWidth: 120,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  approveButton: {
    backgroundColor: '#367E18',
  },
  rejectButton: {
    backgroundColor: '#888888',
  },
  buttonText: {
    color: Colors.background,
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Roboto_bold',
    marginLeft: 6,
  },
  noRequestsText: {
    textAlign: 'center',
    color: Colors.lightText,
    fontStyle: 'italic',
    marginTop: 30,
    marginBottom: 40,
    fontFamily: 'Roboto',
    fontSize: 16,
  },
  caseAcceptanceContent: {
    alignItems: 'center',
    padding: 20,
  },
  caseAcceptanceText: {
    color: Colors.black,
    fontSize: 16,
    fontFamily: 'Roboto',
    marginBottom: 20,
    textAlign: 'center',
  },
  rejectionContainer: {
    width: '100%',
    marginBottom: 20,
  },
  rejectionLabel: {
    color: Colors.black,
    fontSize: 14,
    fontFamily: 'Roboto_bold',
    marginBottom: 8,
  },
  rejectionInput: {
    width: '100%',
    height: 100,
    borderColor: Colors.primary,
    borderWidth: 1,
    borderRadius: 6,
    padding: 8,
    fontSize: 14,
    color: Colors.black,
    fontFamily: 'Roboto',
    backgroundColor: Colors.background,
  },
  caseButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15,
  },
  reportBadge: {
    backgroundColor: '#367E18',
    paddingVertical: 3,
    paddingHorizontal: 6,
    borderRadius: 12,
    marginTop: 4,
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  reportBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  avatarContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
});