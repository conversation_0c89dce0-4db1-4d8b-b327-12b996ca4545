import {
  View,
  Text,
  useWindowDimensions,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  TextInput,
  StyleSheet,
} from "react-native";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import { useEffect, useState, useCallback, useMemo } from "react";
import { router, useLocalSearchParams } from "expo-router";
import Constants from 'expo-constants'; 
import { useAuth } from "../../../context/auth-context";
import { Colors } from "../../../constants/colors";
const BASE_URL = Constants.expoConfig.extra.baseUrl;

// Utility functions remain the same
const formatDate = (dateString) => {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) throw new Error('Invalid date');
    return date.toLocaleDateString('en-GB');
  } catch (e) {
    console.error('Date formatting error:', e);
    return 'Invalid Date';
  }
};

const formatTime = (timeString) => {
  try {
    const date = new Date(timeString);
    if (isNaN(date.getTime())) throw new Error('Invalid time');
    const hours = String(date.getUTCHours()).padStart(2, "0");
    const minutes = String(date.getUTCMinutes()).padStart(2, "0");
    return `${hours}:${minutes}`;
  } catch (e) {
    console.error('Time formatting error:', e);
    return 'Invalid Time';
  }
};

// First, move the styles before the components and update them to use Colors
const createStyles = (width, height) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  searchContainer: {
    marginHorizontal: width * 0.05,
    marginTop: height * 0.02,
    marginBottom: height * 0.01,
  },
  searchInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    fontSize: 16,
  },
  columnHeaderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginHorizontal: width * 0.05,
    marginBottom: height * 0.01,
  },
  columnHeaderText: {
    opacity: 0.5,
    color: Colors.lightText,
  },
  columnTimeContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: width * 0.4,
  },
  caseListItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: width * 0.05,
    paddingVertical: 15,
    borderTopWidth: 1,
    borderColor: Colors.border,
    alignItems: "center",
  },
  caseNameContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  caseName: {
    fontSize: 16,
    color: Colors.black,
  },
  timeContainer: {
    flexDirection: "row",
    width: width * 0.4,
    justifyContent: "space-between",
    alignItems: "center",
  },
  timeText: {
    width: width * 0.2,
    textAlign: "center",
    color: Colors.black,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
    padding: 20,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: Colors.primary,
    padding: 10,
    borderRadius: 5,
  },
  retryButtonText: {
    color: Colors.background,
  },
  statusContainer: {
    flexDirection: "row",
    justifyContent: "flex-start",
    marginHorizontal: width * 0.05,
    marginBottom: height * 0.01,
  },
  statusText: {
    fontSize: 14,
    backgroundColor: "#E6EAF2",
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    color: Colors.primary,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 50,
  },
  listFooter: {
    height: 50,
  },
});

// Update the SearchBar component
const SearchBar = ({ width, height, searchQuery, setSearchQuery, styles }) => (
  <View style={styles.searchContainer}>
    <View style={styles.searchInputWrapper}>
      <FontAwesome name="search" size={20} color={Colors.lightText} />
      <TextInput
        style={styles.searchInput}
        placeholder="Search cases..."
        value={searchQuery}
        onChangeText={setSearchQuery}
      />
      {searchQuery !== '' && (
        <TouchableOpacity onPress={() => setSearchQuery('')}>
          <FontAwesome name="times-circle" size={20} color={Colors.lightText} />
        </TouchableOpacity>
      )}
    </View>
  </View>
);

const ColumnHeaders = ({ width, height, styles }) => (
  <View
    style={styles.columnHeaderContainer}
  >
    <Text style={styles.columnHeaderText}>Case Name</Text>
    <View
      style={styles.columnTimeContainer}
    >
      <Text style={styles.columnHeaderText}>Time</Text>
      <Text style={styles.columnHeaderText}>Date</Text>
    </View>
  </View>
);

const CaseListItem = ({ item, width, onPress, styles }) => (
  <TouchableOpacity
    style={styles.caseListItem}
    onPress={onPress}
  >
    <View style={styles.caseNameContainer}>
      <FontAwesome name="folder" size={24} color="#0B36A1" />
      <Text style={styles.caseName}>{item.CaseName}</Text>
    </View>
    <View
      style={styles.timeContainer}
    >
      <Text style={styles.timeText}>
        {item.time}
      </Text>
      <Text>{item.date}</Text>
    </View>
  </TouchableOpacity>
);

export default function RecentCase() {
  const [cases, setCases] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const { width, height } = useWindowDimensions();
  const { token } = useAuth();
  const params = useLocalSearchParams();
  const status = params.status || 'dispatched'; 
  const styles = useMemo(() => createStyles(width, height), [width, height]);

const filteredCases = useMemo(() => {
  return cases
    .filter(item => {
      if (!item || typeof item.CaseName !== 'string') return false;
      return item.CaseName.toLowerCase().includes(searchQuery.toLowerCase());
    })
    .sort((a, b) => {
      // Convert date strings to Date objects for comparison
      const dateA = new Date(a.date.split('/').reverse().join('-'));
      const dateB = new Date(b.date.split('/').reverse().join('-'));
      
      // If dates are the same, compare times
      if (dateA.getTime() === dateB.getTime()) {
        const timeA = a.time.split(':').map(Number);
        const timeB = b.time.split(':').map(Number);
        
        // Compare hours first, then minutes
        if (timeA[0] === timeB[0]) {
          return timeB[1] - timeA[1]; // Newer times first
        }
        return timeB[0] - timeA[0]; // Newer times first
      }
      
      // Sort by date (newest first)
      return dateB.getTime() - dateA.getTime();
    });
}, [cases, searchQuery]);

  const fetchCases = useCallback(async (page = 1) => {
    try {
      const response = await fetch(
        `${BASE_URL}/api/forensic/lab/case/status?status=${status}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
  
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
  
      const result = await response.json();
      // console.log('Raw API response:', result.data.forensicRequests);
  
      if (!result.data || !Array.isArray(result.data.forensicRequests)) {
        throw new Error('Invalid data format received');
      }
  
      // Format the cases based on the actual response structure
      const formattedCases = result.data.forensicRequests
        .map(item => {
          // console.log('Forensic Request ID:', item._id);
          return {
            id: item._id, 
            CaseName: item.caseId?.title || 'Untitled Case',
            time: formatTime(item.createdAt),
            date: formatDate(item.createdAt),
            status: item.status,
            labName: item.labId?.name || 'Unknown Lab',
            policeStation: item.policeStationId?.name || 'Unknown Police Station',
            evidenceCount: item.evidences?.length || 0
          };
        });
  
      // console.log('Formatted cases:', formattedCases);
  
      if (page === 1) {
        setCases(formattedCases);
      } else {
        setCases(prevCases => [...prevCases, ...formattedCases]);
      }
  
      setTotalPages(result.data.totalPages);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch cases:', err);
      setError('Failed to load cases. Please try again.');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }, [token, status]);

  useEffect(() => {
    fetchCases();
  }, [fetchCases]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    setCurrentPage(1);
    fetchCases(1);
  }, [fetchCases]);

  const loadMoreCases = useCallback(() => {
    if (currentPage < totalPages) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      fetchCases(nextPage);
    }
  }, [currentPage, totalPages, fetchCases]);

  
  const navigateToCase = useCallback((forensicRequestId) => {
    // console.log('Navigating with forensicRequestId:', forensicRequestId);
    router.push({
      pathname: "(screens)/caseDetails",
      params: { forensicRequestId: forensicRequestId },
    });
  }, []);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity 
          onPress={() => fetchCases(1)}
          style={styles.retryButton}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <SearchBar
        width={width}
        height={height}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        styles={styles}
      />
      
      {/* Status indicator */}
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Status: {status.charAt(0).toUpperCase() + status.slice(1)}
        </Text>
      </View>
      
      <ColumnHeaders width={width} height={height} styles={styles} />

      <View style={{ flex: 1 }}>
        <FlatList
          data={filteredCases}
          renderItem={({ item }) => (
            <CaseListItem 
              item={item} 
              width={width} 
              onPress={() => navigateToCase(item.id)}
              styles={styles}
            />
          )}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[Colors.primary]}
              tintColor={Colors.primary}
            />
          }
          onEndReached={loadMoreCases}
          onEndReachedThreshold={0.5}
          ListEmptyComponent={
            <View style={styles.emptyListContainer}>
              <Text>{searchQuery ? 'No matching cases found' : `No ${status} cases found`}</Text>
            </View>
          }
          ListFooterComponent={
            <View style={styles.listFooter} /> 
          }
        />
      </View>
    </View>
  );
}