import {
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    Image,
    StyleSheet,
    FlatList,
    useWindowDimensions,
    Modal,
  } from 'react-native';
  import React, { useState, useEffect } from 'react';
  import VideoPlayer from '../../../components/Larges/VideoPlayer';
  import { router, useLocalSearchParams } from "expo-router";
  import axios from 'axios';
  import { useAuth } from "../../../context/auth-context";
  import { transformUrl } from "../../../utils/transformUrl";
  import Constants from 'expo-constants';
import PreviewComponent from '../../../components/Larges/PreviewComponent';
import { Colors } from '../../../constants/colors';

  
  export default function EvidenceDetails() {
    const { width } = useWindowDimensions();
    const [currentIndex, setCurrentIndex] = useState(0);
    const { evidenceId, caseId, forensicRequestId } = useLocalSearchParams();
    const [loading, setLoading] = useState(true);
    const [evidenceData, setEvidenceData] = useState(null);
    const [error, setError] = useState(null);
    // Add states for preview functionality
    const [previewVisible, setPreviewVisible] = useState(false);
    const [previewUri, setPreviewUri] = useState('');
    
    const BASE_URL = Constants.expoConfig.extra.baseUrl;
    const { token } = useAuth();
    
    useEffect(() => {
      const fetchEvidenceData = async () => {
        try {
          setLoading(true);
          
          const response = await axios.get(
            `${BASE_URL}/api/forensic/request/${forensicRequestId}/evidences`,
            {
              headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
              },
            }
          );
          
          if (response.data.status === 'success') {
            const foundEvidence = response.data.data.evidences.find(
              evidence => evidence._id === evidenceId
            );
            
            if (foundEvidence) {
              if (foundEvidence.attachmentUrl && foundEvidence.attachmentUrl.length > 0) {
                foundEvidence.attachmentUrl = foundEvidence.attachmentUrl.map(url => 
                  transformUrl(url)
                );
              }
              
              setEvidenceData(foundEvidence);
            } else {
              setError('Evidence not found');
            }
          } else {
            setError('Failed to fetch evidence data');
          }
        } catch (err) {
          console.error('Error fetching evidence data:', err);
          setError('An error occurred while fetching evidence data');
        } finally {
          setLoading(false);
        }
      };
      
      fetchEvidenceData();
    }, [evidenceId, forensicRequestId, token, BASE_URL]);
  
    // Function to handle long press on media
    const handleLongPress = (uri) => {
      setPreviewUri(uri);
      setPreviewVisible(true);
    };
  
    // Function to close the preview
    const handleClosePreview = () => {
      setPreviewVisible(false);
    };
  
    if (loading) {
      return (
        <View style={[styles.container, styles.centerContent]}>
          <Text>Loading evidence details...</Text>
        </View>
      );
    }
  
    if (error || !evidenceData) {
      return (
        <View style={[styles.container, styles.centerContent]}>
          <Text style={styles.errorText}>{error || 'No evidence data available'}</Text>
        </View>
      );
    }
    
    // Format the date for display
    const formattedDate = new Date(evidenceData.time).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    });
  
    return (
      <View style={styles.container}>
        <ScrollView>
          {/* Evidence Media Section */}
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionLabel}>Evidence Media</Text>
            <Text style={styles.tooltipText}>Press and hold to preview</Text>
            <FlatList
              data={evidenceData.attachmentUrl}
              renderItem={({ item }) => (
                <TouchableOpacity
                  activeOpacity={0.9}
                  onLongPress={() => handleLongPress(item)}
                  delayLongPress={200}
                >
                  {item.endsWith('.mp4') ? (
                    <VideoPlayer 
                      uri={item} 
                      style={{ width: width - 32, height: 240 }} 
                    />
                  ) : (
                    <Image
                      source={{ uri: item }}
                      style={[styles.evidenceMedia, { width: width - 32 }]}
                      resizeMode="cover"
                    />
                  )}
                </TouchableOpacity>
              )}
              keyExtractor={(_, index) => index.toString()}
              horizontal
              pagingEnabled
              showsHorizontalScrollIndicator={false}
              snapToInterval={width - 32}
              decelerationRate="fast"
              onScroll={(event) => {
                const offset = event.nativeEvent.contentOffset.x;
                const index = Math.round(offset / (width - 32));
                setCurrentIndex(index);
              }}
              contentContainerStyle={styles.mediaListContainer}
            />
  
            <View style={styles.swipeIndicatorContainer}>
              {evidenceData.attachmentUrl.map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.swipeIndicator,
                    index === currentIndex && styles.activeSwipeIndicator,
                  ]}
                />
              ))}
            </View>
          </View>
  
          {/* Evidence Details Section */}
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionLabel}>Evidence Information</Text>
            <View style={styles.detailsCard}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Type:</Text>
                <Text style={styles.detailValue}>{evidenceData.type}</Text>
              </View>
  
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Title:</Text>
                <Text style={styles.detailValue}>{evidenceData.title}</Text>
              </View>
  
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Description:</Text>
                <Text style={styles.detailValue}>{evidenceData.description}</Text>
              </View>
  
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Date & Time:</Text>
                <Text style={styles.detailValue}>{formattedDate}</Text>
              </View>
  
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Location:</Text>
                <Text style={styles.detailValue}>{evidenceData.gpsLocation}</Text>
              </View>
            </View>
          </View>
        </ScrollView>
  
        {/* Preview Modal */}
        <Modal
          visible={previewVisible}
          transparent={true}
          animationType="fade"
          onRequestClose={handleClosePreview}
        >
          <PreviewComponent 
            uri={previewUri}
            onClose={handleClosePreview}
          />
        </Modal>
      </View>
    );
  }
  
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: Colors.background,
    },
    sectionContainer: {
      marginBottom: 20,
      paddingTop: 12,
    },
    mediaListContainer: {
      paddingHorizontal: 16,
    },
    sectionLabel: {
      fontSize: 20,
      fontWeight: 'bold',
      fontFamily: 'Roboto_bold',
      color: Colors.primary,
      marginBottom: 12,
      paddingHorizontal: 16,
    },
    evidenceMedia: {
      height: 240,
      borderRadius: 8,
      marginRight: 16,
    },
    detailsCard: {
      backgroundColor: Colors.background,
      borderRadius: 12,
      padding: 16,
      marginHorizontal: 16,
      shadowColor: Colors.black,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    detailRow: {
      flexDirection: 'row',
      paddingVertical: 10,
      borderBottomWidth: 1,
      borderBottomColor: Colors.border,
    },
    detailLabel: {
      width: '40%',
      fontSize: 14,
      fontWeight: '500',
      color: Colors.lightText,
    },
    detailValue: {
      flex: 1,
      fontSize: 14,
      color: Colors.black,
    },
    swipeIndicatorContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 12,
    },
    swipeIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: Colors.lightText,
      marginHorizontal: 4,
    },
    activeSwipeIndicator: {
      backgroundColor: Colors.primary,
      width: 10,
      height: 10,
      borderRadius: 5,
    },
    tooltipText: {
      fontSize: 14,
      color: Colors.lightText,
      fontStyle: 'italic',
      marginBottom: 8,
      paddingHorizontal: 16,
      textAlign: 'center',
    },
    centerContent: {
      justifyContent: 'center',
      alignItems: 'center',
      paddingTop: 50,
    },
    errorText: {
      color: Colors.error,
      fontSize: 16,
      textAlign: 'center',
      fontFamily: 'Roboto',
    }
  });