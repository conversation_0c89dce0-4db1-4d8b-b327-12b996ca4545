import { View, Text, StyleSheet, TouchableOpacity, ScrollView, StatusBar, SafeAreaView, Switch } from 'react-native';
import React, { useState } from 'react';
import { useAuth } from '../../../../context/auth-context';
import { Ionicons } from '@expo/vector-icons';
import { useAppUpdates } from '../../../../hooks/useAppUpdates';

const forensicSettings = () => {
  const { logout } = useAuth();
  const { checkForUpdates, isChecking } = useAppUpdates();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [darkModeEnabled, setDarkModeEnabled] = useState(false);
  const [biometricEnabled, setBiometricEnabled] = useState(false);

  const handleLogout = () => {
    logout();
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style="dark" />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* App Settings Section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>App Settings</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="notifications-outline" size={20} color="#0a34a1" style={styles.settingIcon} />
              <Text style={styles.settingText}>Notifications</Text>
            </View>
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              trackColor={{ false: "#d1d1d1", true: "#b5c4e3" }}
              thumbColor={notificationsEnabled ? "#0a34a1" : "#f4f3f4"}
            />
          </View>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="moon-outline" size={20} color="#0a34a1" style={styles.settingIcon} />
              <Text style={styles.settingText}>Dark Mode</Text>
            </View>
            <Switch
              value={darkModeEnabled}
              onValueChange={setDarkModeEnabled}
              trackColor={{ false: "#d1d1d1", true: "#b5c4e3" }}
              thumbColor={darkModeEnabled ? "#0a34a1" : "#f4f3f4"}
            />
          </View>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="finger-print-outline" size={20} color="#0a34a1" style={styles.settingIcon} />
              <Text style={styles.settingText}>Biometric Login</Text>
            </View>
            <Switch
              value={biometricEnabled}
              onValueChange={setBiometricEnabled}
              trackColor={{ false: "#d1d1d1", true: "#b5c4e3" }}
              thumbColor={biometricEnabled ? "#0a34a1" : "#f4f3f4"}
            />
          </View>

          <TouchableOpacity 
            style={styles.settingItem} 
            onPress={checkForUpdates}
            disabled={isChecking}
          >
            <View style={styles.settingInfo}>
              <Ionicons name="refresh-outline" size={20} color="#0a34a1" style={styles.settingIcon} />
              <View>
                <Text style={styles.settingText}>Check for Updates</Text>
                <Text style={styles.settingDescription}>Update to the latest version</Text>
              </View>
            </View>
            {isChecking ? (
              <Ionicons name="reload" size={20} color="#0a34a1" />
            ) : (
              <Ionicons name="chevron-forward" size={18} color="#888" />
            )}
          </TouchableOpacity>
        </View>

        {/* More Options Section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>More Options</Text>
          
          <TouchableOpacity style={styles.optionItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="shield-checkmark-outline" size={20} color="#0a34a1" style={styles.settingIcon} />
              <Text style={styles.settingText}>Privacy Policy</Text>
            </View>
            <Ionicons name="chevron-forward" size={18} color="#888" />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.optionItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="document-text-outline" size={20} color="#0a34a1" style={styles.settingIcon} />
              <Text style={styles.settingText}>Terms of Service</Text>
            </View>
            <Ionicons name="chevron-forward" size={18} color="#888" />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.optionItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="help-circle-outline" size={20} color="#0a34a1" style={styles.settingIcon} />
              <Text style={styles.settingText}>Help & Support</Text>
            </View>
            <Ionicons name="chevron-forward" size={18} color="#888" />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.optionItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="information-circle-outline" size={20} color="#0a34a1" style={styles.settingIcon} />
              <Text style={styles.settingText}>About</Text>
            </View>
            <Ionicons name="chevron-forward" size={18} color="#888" />
          </TouchableOpacity>
        </View>

        {/* Logout Button */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Ionicons name="log-out-outline" size={20} color="#fff" style={styles.logoutIcon} />
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContainer: {
    flexGrow: 1,
  },
  sectionContainer: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 10,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0a34a1',
    marginBottom: 15,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIcon: {
    marginRight: 15,
  },
  settingText: {
    fontSize: 15,
    color: '#333',
  },
  settingDescription: {
    fontSize: 12,
    color: '#888',
    marginTop: 2,
  },
  logoutButton: {
    flexDirection: 'row',
    backgroundColor: '#d9534f',
    marginHorizontal: 20,
    marginTop: 30,
    marginBottom: 30,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoutIcon: {
    marginRight: 10,
  },
  logoutText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 15,
  },
});

export default forensicSettings;