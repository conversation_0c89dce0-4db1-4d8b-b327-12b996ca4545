import React from 'react';
import { Tabs } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import CustomHeader from '../../../../components/Larges/CustomHeader';
import { SafeAreaView } from 'react-native-safe-area-context';

const TabsLayoutOfficer = () => {
  const handleSearchPress = () => {
    console.log('Search pressed');
  };

  const handleNotificationPress = () => {
    console.log('Notification pressed');
  };

  const getTitle = (routeName) => {
    switch (routeName) {
      case 'index':
        return 'Forensic Home';
      case 'profile':
        return 'Profile';
      case 'settings':
        return 'Settings';
      default:
        return 'App';
    }
  };

  return (
    <Tabs
      screenOptions={({ route }) => ({
        tabBarActiveTintColor: '#0546a1',
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          backgroundColor: '#f8f8f8',
          borderTopWidth: 1,
          borderTopColor: '#e7e7e7',
        },
        header: () => (
          <CustomHeader
            title={getTitle(route.name)}
            showMenu={true}
            menuColor="#979797"
            showSearch={false}
            showNotification={true}
            notificationHandler={handleNotificationPress}
            notificationIconName="notifications-outline"
            notificationIconColor="#979797"
            headerBackgroundColor="#fff"
            titleColor="#333333"
          />
        ),
      })}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Forensic Home',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="home" size={size} color={color} />
          ),
          tabBarLabel: 'Forensic Home',
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="person" size={size} color={color} />
          ),
          tabBarLabel: 'Profile',
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: 'Settings',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="settings" size={size} color={color} />
          ),
          tabBarLabel: 'Settings',
        }}
      />
    </Tabs>
  );
};

export default TabsLayoutOfficer;