import { useEffect, useState } from 'react';
import { useRouter } from 'expo-router';
import { useAuth } from '../context/auth-context';
import LoadingScreen from '../components/Larges/LoadingScreen';

export default function Index() {
  const { user, isLoading } = useAuth();
  const [showLoader, setShowLoader] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowLoader(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!isLoading && !showLoader) {
      if (!user || !user.token) {
        router.replace('/(auth)/welcome');
        return;
      }


      if (user.category === 'forensic') {
        router.replace('/(forensic)');
        return;
      }

      router.replace('/(auth)/welcome');
    }
  }, [isLoading, showLoader, user, router]);

  if (isLoading || showLoader) {
    return (
      <LoadingScreen
        loadingText="Syncing data with server..."
        primaryColor="#0a34a1"
        secondaryColor="#fff"
      />
    );
  }

  return null;
}