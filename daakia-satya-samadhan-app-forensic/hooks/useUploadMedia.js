import { useState } from 'react';
import { apiService } from '../services/api';
import { AUTH_ERRORS } from '../constants/auth';

const useUploadMedia = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState(null);

  const uploadMedia = async (uri, type) => {
    setIsUploading(true);
    setError(null);

    const formData = new FormData();

    let fileExtension = '';
    let mimeType = '';

    switch (type) {
      case 'image':
        fileExtension = 'jpg';
        mimeType = 'image/jpeg';
        break;
      case 'video':
        fileExtension = 'mp4';
        mimeType = 'video/mp4';
        break;
      case 'pdf':
        fileExtension = 'pdf';
        mimeType = 'application/pdf';
        break;
      default:
        fileExtension = 'file';
        mimeType = 'application/octet-stream';
    }

    const file = {
      uri,
      name: `file-${Date.now()}.${fileExtension}`,
      type: mimeType,
    };

    formData.append('file', file);

    try {
      const result = await apiService.uploadFile(formData);

      if (result.status === 'success') {
        return result.data.fileUrl;
      } else {
        setError(result.message || AUTH_ERRORS.UPLOAD_ERROR);
        return null;
      }
    } catch (error) {
      setError(error.message || AUTH_ERRORS.UPLOAD_ERROR);
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  return { uploadMedia, isUploading, error };
};

export default useUploadMedia;