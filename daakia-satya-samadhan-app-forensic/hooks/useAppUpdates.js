import * as Updates from 'expo-updates';
import { useState } from 'react';
import { Alert, Linking } from 'react-native';
import Constants from 'expo-constants';

export const useAppUpdates = () => {
  const [isChecking, setIsChecking] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateAvailable, setUpdateAvailable] = useState(false);

  // Default update configuration
  const defaultUpdateConfig = {
    showCustomMessage: false,
    customMessage: 'A new version of the app is available.',
    customLink: null,
    customLinkText: 'Download Now',
  };

  const [updateConfig, setUpdateConfig] = useState(defaultUpdateConfig);

  // Function to configure custom update message and link
  const configureUpdateMessage = (config) => {
    setUpdateConfig({
      ...defaultUpdateConfig,
      ...config,
    });
  };

  const checkForUpdates = async () => {
    try {
      setIsChecking(true);
      
      // Check if we're in development mode
      if (__DEV__) {
        Alert.alert(
          'Development Mode',
          'App updates are not available in development mode. Please build a production version to test updates.',
          [{ text: 'OK' }]
        );
        return;
      }

      const update = await Updates.checkForUpdateAsync();
      
      if (update.isAvailable) {
        setUpdateAvailable(true);
        
        if (updateConfig.showCustomMessage) {
          // Show custom message with link if configured
          Alert.alert(
            'Update Available',
            updateConfig.customMessage,
            [
              updateConfig.customLink ? {
                text: updateConfig.customLinkText,
                onPress: () => Linking.openURL(updateConfig.customLink),
              } : null,
              {
                text: 'Update Now',
                onPress: () => fetchAndReloadUpdate(),
              },
              {
                text: 'Later',
                style: 'cancel',
              },
            ].filter(Boolean),
            { cancelable: false }
          );
        } else {
          // Show default update message
          Alert.alert(
            'Update Available',
            'A new version of the app is available. Would you like to update now?',
            [
              {
                text: 'Update Now',
                onPress: () => fetchAndReloadUpdate(),
              },
              {
                text: 'Later',
                style: 'cancel',
              },
            ],
            { cancelable: false }
          );
        }
      } else {
        Alert.alert('No Updates', 'You are using the latest version of the app.');
      }
    } catch (error) {
      console.error('Error checking for updates:', error);
      Alert.alert(
        'Update Check Failed',
        'Unable to check for updates. Please try again later.'
      );
    } finally {
      setIsChecking(false);
    }
  };

  const fetchAndReloadUpdate = async () => {
    try {
      setIsUpdating(true);
      Alert.alert(
        'Updating App',
        'Please wait while we update your app...',
        [],
        { cancelable: false }
      );
      
      await Updates.fetchUpdateAsync();
      await Updates.reloadAsync();
    } catch (error) {
      console.error('Error updating app:', error);
      Alert.alert(
        'Update Failed',
        'Failed to update the app. Please try again later.'
      );
    } finally {
      setIsUpdating(false);
    }
  };

  return {
    isChecking,
    isUpdating,
    updateAvailable,
    checkForUpdates,
    fetchAndReloadUpdate,
    configureUpdateMessage,
  };
}; 