import axios from 'axios';
import Constants from 'expo-constants';
import { API_ENDPOINTS, AUTH_ERRORS } from '../constants/auth';

const BASE_URL = Constants.expoConfig?.extra?.baseUrl;

if (!BASE_URL) {
  throw new Error(AUTH_ERRORS.BASE_URL_MISSING);
}

const createBaseInstance = (token = '') => {
  const instance = axios.create({
    baseURL: BASE_URL,
    timeout: 10000, // 10 seconds timeout
    headers: {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
  });

  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        throw new Error(AUTH_ERRORS.TOKEN_EXPIRED);
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

// Generic request handler
const makeRequest = async (method, endpoint, data = null, token = '', customHeaders = {}) => {
  const instance = createBaseInstance(token);
  const config = {
    headers: { ...customHeaders },
  };

  try {
    const response = method === 'get' 
      ? await instance[method](endpoint, config)
      : await instance[method](endpoint, data, config);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const apiService = {
  // ===== AUTHENTICATION & TOKEN MANAGEMENT =====
  login: (mobileNumber) => 
    makeRequest('post', API_ENDPOINTS.LOGIN, { mobileNumber }, '', { 'category': 'forensic' }),

  verifyOtp: (userId, requestId, otp) => 
    makeRequest('post', API_ENDPOINTS.VERIFY_OTP, { userId, requestId, otp }),

  refreshToken: async (token) => {
    try {
      const response = await makeRequest('post', API_ENDPOINTS.REFRESH_TOKEN, null, token);
      if (response.status === 'success' && response.data.token) {
        return response.data.token;
      }
      throw new Error(AUTH_ERRORS.TOKEN_REFRESH_FAILED);
    } catch (error) {
      throw new Error(AUTH_ERRORS.TOKEN_REFRESH_FAILED);
    }
  },

  // ===== USER PROFILE MANAGEMENT =====
  fetchProfile: (token) => 
    makeRequest('get', API_ENDPOINTS.PROFILE, null, token),

  // ===== REGISTRATION & FORENSIC MANAGEMENT =====
  register: (token, data) => 
    makeRequest('post', API_ENDPOINTS.REGISTER, data, token),

  fetchLabs: (token) => 
    makeRequest('get', API_ENDPOINTS.FETCH_LABS, null, token),

  fetchDepartments: (token, labId) => 
    makeRequest('get', API_ENDPOINTS.FETCH_DEPARTMENTS.replace(':labId', labId), null, token),

  // ===== FILE UPLOAD =====
  uploadFile: async (formData) => {
    try {
      const response = await axios.post(`${BASE_URL}${API_ENDPOINTS.UPLOAD}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || AUTH_ERRORS.UPLOAD_ERROR);
    }
  },

  // ===== REPORT SUBMISSION =====
  submitReport: (token, caseId, evidenceId, reportData) => {
    const endpoint = API_ENDPOINTS.SUBMIT_REPORT
      .replace(':caseId', caseId)
      .replace(':evidenceId', evidenceId);
    return makeRequest('post', endpoint, reportData, token);
  },
}; 