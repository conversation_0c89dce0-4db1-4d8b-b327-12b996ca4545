import { AUTH_ERRORS } from '../constants/auth';

export const validationService = {
  validateMobile: (mobileNumber) => {
    if (!mobileNumber || mobileNumber.length !== 10) {
      throw new Error(AUTH_ERRORS.INVALID_MOBILE);
    }
  },

  validateOtp: (otp) => {
    if (!otp || otp.length !== 5) {
      throw new Error(AUTH_ERRORS.INVALID_OTP);
    }
  },

  validateLoginData: (mobileNumber) => {
    validationService.validateMobile(mobileNumber);
  },

  validateOtpVerification: (userId, requestId, otp) => {
    if (!userId || !requestId) {
      throw new Error(AUTH_ERRORS.MISSING_AUTH);
    }
    validationService.validateOtp(otp);
  },

  validateName: (name) => {
    if (!name || name.trim().length < 2) {
      throw new Error(AUTH_ERRORS.INVALID_NAME);
    }
  },

  validateAadhar: (aadhar) => {
    if (!aadhar || aadhar.length !== 12 || isNaN(aadhar)) {
      throw new Error(AUTH_ERRORS.INVALID_AADHAR);
    }
  },

  validateEmail: (email) => {
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      throw new Error(AUTH_ERRORS.INVALID_EMAIL);
    }
  },

  validateRegistrationData: (data) => {
    const { name, mobileNumber, aadhar, emailId, lab, labDepartment, displayUrl } = data;

    validationService.validateName(name);
    validationService.validateMobile(mobileNumber);
    validationService.validateAadhar(aadhar);
    validationService.validateEmail(emailId);

    if (!lab) throw new Error(AUTH_ERRORS.MISSING_LAB);
    if (!labDepartment) throw new Error(AUTH_ERRORS.MISSING_DEPARTMENT);
    if (!displayUrl || typeof displayUrl !== 'string' || !displayUrl.trim()) {
      throw new Error(AUTH_ERRORS.MISSING_PROFILE_PIC);
    }
  },

  validateReportData: (title, description, media) => {
    if (!title || !title.trim()) {
      throw new Error(AUTH_ERRORS.MISSING_TITLE);
    }

    if (!description || !description.trim()) {
      throw new Error(AUTH_ERRORS.MISSING_DESCRIPTION);
    }

    if (!media || media.length === 0) {
      throw new Error(AUTH_ERRORS.MISSING_MEDIA);
    }
  },
}; 