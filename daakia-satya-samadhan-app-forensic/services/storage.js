import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants/auth';

export const storageService = {
  setItem: async (key, value) => {
    try {
      const valueToStore = typeof value === 'object' ? JSON.stringify(value) : value;
      await AsyncStorage.setItem(key, valueToStore);
    } catch (error) {
      console.error(`Error storing ${key}:`, error);
      throw error;
    }
  },

  getItem: async (key) => {
    try {
      const value = await AsyncStorage.getItem(key);
      try {
        return value ? JSON.parse(value) : null;
      } catch {
        return value; // Return as is if not JSON
      }
    } catch (error) {
      console.error(`Error retrieving ${key}:`, error);
      throw error;
    }
  },

  removeItem: async (key) => {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error(`Error removing ${key}:`, error);
      throw error;
    }
  },

  clearAuth: async () => {
    try {
      await AsyncStorage.multiRemove(Object.values(STORAGE_KEYS));
    } catch (error) {
      console.error('Error clearing auth storage:', error);
      throw error;
    }
  },
}; 