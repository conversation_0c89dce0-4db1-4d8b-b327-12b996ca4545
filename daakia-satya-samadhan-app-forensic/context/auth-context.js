import React, { createContext, useContext, useState, useEffect } from 'react';
import * as Updates from 'expo-updates';
import { AUTH_ERRORS, STORAGE_KEYS, TOKEN_REFRESH_INTERVAL } from '../constants/auth';
import { validationService } from '../services/validation';
import { storageService } from '../services/storage';
import { apiService } from '../services/api';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedRole, setSelectedRole] = useState(null);
  const [error, setError] = useState(null);

  const refreshToken = async () => {
    try {
      const currentToken = await storageService.getItem(STORAGE_KEYS.TOKEN);
      if (!currentToken) throw new Error(AUTH_ERRORS.MISSING_AUTH);
      const newToken = await apiService.refreshToken(currentToken);
      await storageService.setItem(STORAGE_KEYS.TOKEN, newToken);
      setUser(prev => ({ ...prev, token: newToken }));
      return newToken;
    } catch (error) {
      await logout();
      throw error;
    }
  };

  useEffect(() => {
    const loadAuthState = async () => {
      try {
        const token = await storageService.getItem(STORAGE_KEYS.TOKEN);
        const roles = await storageService.getItem(STORAGE_KEYS.ROLES);
        const category = await storageService.getItem(STORAGE_KEYS.CATEGORY);
        const userId = await storageService.getItem(STORAGE_KEYS.USER_ID);
        const requestId = await storageService.getItem(STORAGE_KEYS.REQUEST_ID);
        const storedSelectedRole = await storageService.getItem(STORAGE_KEYS.SELECTED_ROLE);
        if (token) {
          setUser({ token, roles, category, userId, requestId });
          setSelectedRole(storedSelectedRole);
          try {
            await fetchUserProfile(token);
          } catch (error) {
            if (error.message === AUTH_ERRORS.TOKEN_EXPIRED) {
              await logout();
            }
          }
        }
      } catch (error) {
        console.error('Error loading auth state:', error);
      } finally {
        setIsLoading(false);
      }
    };
    loadAuthState();
  }, []);

  useEffect(() => {
    const tokenRefreshInterval = setInterval(async () => {
      const token = await storageService.getItem(STORAGE_KEYS.TOKEN);
      if (token) {
        try {
          await refreshToken();
        } catch (error) {
          console.error('Scheduled token refresh failed:', error);
        }
      }
    }, TOKEN_REFRESH_INTERVAL);
    return () => clearInterval(tokenRefreshInterval);
  }, []);

  const fetchUserProfile = async (token) => {
    try {
      const response = await apiService.fetchProfile(token);
      if (response.status === 'success') {
        setProfile(response.data);
      } else {
        throw new Error(AUTH_ERRORS.PROFILE_FETCH_FAILED);
      }
    } catch (error) {
      throw error;
    }
  };

  const login = async (mobileNumber) => {
    setError(null);
    try {
      validationService.validateLoginData(mobileNumber);
      setIsLoading(true);
      const response = await apiService.login(mobileNumber);
      if (response.status === 'success') {
        return {
          userId: response.data.userId,
          requestId: response.data.requestId,
        };
      }
      throw new Error(response.message || 'Login failed');
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOtp = async (userId, requestId, otp) => {
    setError(null);
    try {
      validationService.validateOtpVerification(userId, requestId, otp);
      setIsLoading(true);
      const response = await apiService.verifyOtp(userId, requestId, otp);
      if (response.status === 'success') {
        const { role: roles, token, category } = response.data;
        const defaultRole = roles.includes('officer') ? 'officer' : roles[0];
        await Promise.all([
          storageService.setItem(STORAGE_KEYS.TOKEN, token),
          storageService.setItem(STORAGE_KEYS.ROLES, roles),
          storageService.setItem(STORAGE_KEYS.SELECTED_ROLE, defaultRole),
          storageService.setItem(STORAGE_KEYS.CATEGORY, category),
          storageService.setItem(STORAGE_KEYS.USER_ID, userId),
          storageService.setItem(STORAGE_KEYS.REQUEST_ID, requestId),
        ]);
        setUser({ token, roles, category, userId, requestId });
        setSelectedRole(defaultRole);
        await fetchUserProfile(token);
        return true;
      }
      throw new Error(response.message || 'OTP verification failed');
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await storageService.clearAuth();
      setUser(null);
      setProfile(null);
      setSelectedRole(null);
      await Updates.reloadAsync();
    } catch (error) {
      setUser(null);
      setProfile(null);
      setSelectedRole(null);
    }
  };

  const updateSelectedRole = async (newRole) => {
    if (!user?.roles.includes(newRole)) {
      throw new Error(AUTH_ERRORS.INVALID_ROLE);
    }
    await storageService.setItem(STORAGE_KEYS.SELECTED_ROLE, newRole);
    setSelectedRole(newRole);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        profile,
        isLoading,
        login,
        verifyOtp,
        logout,
        selectedRole,
        updateSelectedRole,
        userId: user?.userId || null,
        requestId: user?.requestId || null,
        token: user?.token || null,
        refreshToken,
        error,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);