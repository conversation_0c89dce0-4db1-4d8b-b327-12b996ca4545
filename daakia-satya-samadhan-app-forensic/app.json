{"expo": {"scheme": "myapp", "name": "<PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-Forensic", "version": "0.22.19", "orientation": "portrait", "icon": "./assets/images/android-icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/images/android-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff", "userInterfaceStyle": "light"}, "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "Allow <PERSON><PERSON><PERSON> to access your camera"}, "bundleIdentifier": "com.daakia.satyaSamadhanForensic.app"}, "android": {"package": "com.daakia.satyaSamadhanForensic.app", "adaptiveIcon": {"foregroundImage": "./assets/images/android-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.ACCESS_FINE_LOCATION", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.MODIFY_AUDIO_SETTINGS"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/android-icon.png"}, "extra": {"baseUrl": "https://f71e-2401-4900-1f26-7ad9-9d62-ba9b-88a2-fd4f.ngrok-free.app", "router": {"origin": false}, "eas": {"projectId": "04d0481a-a367-435a-a162-cc0ec81f4c91"}}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/android-icon.png", "resizeMode": "cover", "imageWidth": 200}], ["expo-media-library", {"photosPermission": "Allow <PERSON><PERSON><PERSON> to access your photos.", "savePhotosPermission": "Allow <PERSON><PERSON><PERSON> to save photos.", "isAccessMediaLocationEnabled": false}], ["expo-image-picker", {"photosPermission": "Allow <PERSON><PERSON><PERSON>ens<PERSON> to access your photos to enable image selection.", "cameraPermission": "Allow <PERSON><PERSON><PERSON> to use your camera for capturing photos."}], ["expo-camera", {"cameraPermission": "Allow <PERSON><PERSON><PERSON> to access your camera for photo and video capture.", "microphonePermission": "Allow <PERSON><PERSON><PERSON> to access your microphone for recording audio during video capture."}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow <PERSON><PERSON><PERSON>han Forensic to access your location to provide location-based services."}], ["expo-av", {"microphonePermission": "Allow <PERSON><PERSON><PERSON> to access your microphone."}], "expo-secure-store", "expo-video", "react-native-vision-camera", ["react-native-document-scanner-plugin", {"cameraPermission": "Allow <PERSON><PERSON><PERSON> to access your camera for document scanning."}]], "runtimeVersion": "0.22.19", "experiments": {"typedRoutes": true}}}