import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet } from 'react-native';

const FormSection = ({ description, onDescriptionChange, onSubmit , padding, paddingHorizontal  }) => {
    return (
      <View style={[styles.formSection , { padding , paddingHorizontal}]}>
        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>
            Description Here <Text style={styles.requiredAsterisk}>*</Text>
          </Text>
          <TextInput
            style={styles.descriptionInput}
            placeholder="Describe the detail..."
            multiline
            numberOfLines={4}
            value={description}
            onChangeText={onDescriptionChange}
          />
        </View>
        <View>
          <TouchableOpacity style={styles.submitButton} onPress={onSubmit}>
            <Text style={styles.submitButtonText}>SUBMIT</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

const styles = StyleSheet.create({
  formSection: {
    padding: 0,
    paddingHorizontal:0,
  },
  formField: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#333',
    fontFamily: 'Roboto',
  },
  requiredAsterisk: {
    color: 'red',
  },
  descriptionInput: {
    borderWidth: 1,
    borderColor: '#BFC7D2',
    borderRadius: 8,
    padding: 12,
    minHeight: 100,
    textAlignVertical: 'top',
    fontFamily: 'Roboto',
  },
  submitButton: {
    backgroundColor: '#0B36A1',
    padding: 15,
    borderRadius: 50,
    alignItems: 'center',
    fontFamily: 'Roboto',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Roboto',
  },
});

export default FormSection;