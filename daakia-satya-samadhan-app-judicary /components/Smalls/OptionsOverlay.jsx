import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Dimensions, Modal } from 'react-native';

const { width } = Dimensions.get('window');

const OptionsOverlay = ({ visible, onSelectOption, onClose }) => {
  return (
    <Modal
      transparent={true}
      visible={visible}
      onRequestClose={onClose} 
    >
      <View style={styles.modalContainer}>
        <View style={styles.optionsOverlay}>
          <TouchableOpacity
            style={styles.optionButton}
            onPress={() => onSelectOption('photo')}
          >
            <Text style={styles.optionText}>Take Photo</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.optionButton}
            onPress={() => onSelectOption('video')}
          >
            <Text style={styles.optionText}>Take Video</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
          >
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent background
  },
  optionsOverlay: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 20,
    width: width * 0.8, // 80% of screen width
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.8,
    shadowRadius: 2,
  },
  optionButton: {
    padding: 15,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  optionText: {
    fontSize: 16,
    color: '#333',
    fontFamily: 'Roboto',
  },
  closeButton: {
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  closeButtonText: {
    fontSize: 16,
    color: '#ff4444', // Red color for close button
    fontFamily: 'Roboto',
  },
});

export default OptionsOverlay;