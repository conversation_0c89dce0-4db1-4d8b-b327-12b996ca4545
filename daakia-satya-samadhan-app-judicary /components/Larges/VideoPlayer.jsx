import React, { useEffect, useState } from 'react';
import { StyleSheet, View, ActivityIndicator, Text, TouchableOpacity } from 'react-native';
import { useVideoPlayer, VideoView } from 'expo-video';

const VideoPlayer = ({ uri, style }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(true); 
  const [error, setError] = useState(null); 
  const player = useVideoPlayer(uri, (player) => {
    // Remove or comment out the auto-play
    // player.play();
  });

  useEffect(() => {
    const loadingSubscription = player.addListener('statusChange', ({ status }) => {
      if (status === 'readyToPlay') {
        setIsLoading(false); 
      } else if (status === 'loading') {
        setIsLoading(true); 
      }
    });

    return () => {
      loadingSubscription.remove();
    };
  }, [player]);


  useEffect(() => {
    const errorSubscription = player.addListener('error', (error) => {
      setError('Failed to load video. Please try again.'); 
      setIsLoading(false);
    });

    return () => {
      errorSubscription.remove();
    };
  }, [player]);


  useEffect(() => {
    const playingSubscription = player.addListener('playingChange', (isPlaying) => {
      setIsPlaying(isPlaying);
    });

    return () => {
      playingSubscription.remove();
    };
  }, [player]);

 
  const togglePlayPause = () => {
    if (isPlaying) {
      player.pause();
    } else {
      player.play();
    }
  };

  return (
    <View style={[styles.container, style]}>
   
      <VideoView
        style={styles.video}
        player={player}
        allowsFullscreen
        allowsPictureInPicture
      />

      {isLoading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#0B36A1" />
          <Text style={styles.loaderText}>Please Wait Video is Loading</Text>
        </View>
      )}

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={() => setError(null)} style={styles.retryButton}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Play/Pause Button
      {!isLoading && !error && (
        <TouchableOpacity onPress={togglePlayPause} style={styles.playPauseButton}>
          <Text style={styles.playPauseButtonText}>
            {isPlaying ? 'Pause' : 'Play'}
          </Text>
        </TouchableOpacity>
      )} */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#000', 
  },
  video: {
    width: '100%',
    aspectRatio: 16 / 9,
  },
  loaderContainer: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderText: {
    marginTop: 8,
    color: '#FFF',
    fontSize: 14,
  },
  errorContainer: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 16,
    borderRadius: 8,
  },
  errorText: {
    color: '#EB5757',
    fontSize: 14,
    marginBottom: 8,
  },
  retryButton: {
    backgroundColor: '#0B36A1',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
  },
  retryButtonText: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '500',
  },
  playPauseButton: {
    position: 'absolute',
    bottom: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
  },
  playPauseButtonText: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default VideoPlayer;