import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './styles/scheduleHearing.styles';

const AgendaView = ({ events = [], formatEventTime }) => {
  return (
    <ScrollView style={styles.agendaContainer}>
      {events.length > 0 ? (
        events
          .sort((a, b) => a.start - b.start)
          .map((item, index) => (
            <View key={index} style={styles.agendaItem}>
              <View style={styles.agendaDateSection}>
                <Text style={styles.agendaDate}>
                  {item.start.toLocaleDateString('default', { weekday: 'short' })}
                </Text>
                <Text style={styles.agendaDateNumber}>
                  {item.start.getDate()}
                </Text>
              </View>
              <View style={[styles.agendaEventColor, { backgroundColor: item.color }]} />
              <View style={styles.agendaEventDetails}>
                <Text style={styles.agendaEventTitle}>{item.title}</Text>
                <Text style={styles.agendaEventTime}>
                  {formatEventTime(item.start)} - {formatEventTime(item.end)}
                </Text>
                <Text style={styles.agendaEventDescription}>{item.details}</Text>
              </View>
            </View>
          ))
      ) : (
        <View style={styles.noEventsContainer}>
          <Ionicons name="calendar-outline" size={48} color="#ccc" />
          <Text style={styles.noEventsText}>No upcoming events</Text>
        </View>
      )}
    </ScrollView>
  );
};

export default AgendaView;
