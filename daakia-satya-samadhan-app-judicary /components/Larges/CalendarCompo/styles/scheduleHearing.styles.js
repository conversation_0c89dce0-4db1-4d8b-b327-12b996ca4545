import { StyleSheet, Dimensions } from 'react-native';
import { theme } from '../../../../constants/colors';
import { Colors } from '../../../../constants/colors';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const DRAWER_WIDTH = Math.min(SCREEN_WIDTH * 0.8, 300);
const IS_TABLET = SCREEN_WIDTH >= 768;

export const styles = StyleSheet.create({
  pageContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: IS_TABLET ? 16 : 10,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.background,
    zIndex: 10,
    flexWrap: 'wrap',
  },
  contentContainer: {
    flex: 1,
    position: 'relative',
    overflow: 'hidden',
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    flexWrap: 'wrap',
  },
  menuButton: {
    marginRight: 12,
  },
  dateRangeText: {
    fontSize: IS_TABLET ? 18 : 16,
    fontWeight: '500',
    marginRight: 10, // Return to original margin
    fontFamily: 'Roboto',
    color: theme.colors.text.dark,
    flexShrink: 1,
  },
  weekButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    paddingHorizontal: IS_TABLET ? 14 : 10,
    paddingVertical: IS_TABLET ? 8 : 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginLeft: IS_TABLET ? 20 : 15, // Add margin to create gap
    // Add shadow for better visibility
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  weekButtonText: {
    color: theme.colors.primary,
    marginRight: 6,
    fontFamily: 'Roboto',
    fontSize: IS_TABLET ? 15 : 14,
    fontWeight: '500',
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: IS_TABLET ? 0 : 8,
  },
  todayButton: {
    paddingHorizontal: IS_TABLET ? 12 : 8,
    paddingVertical: IS_TABLET ? 6 : 4,
    borderRadius: 4,
    backgroundColor: theme.colors.background,
    marginRight: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  todayButtonText: {
    color: theme.colors.primary,
    fontFamily: 'Roboto',
    fontSize: IS_TABLET ? 14 : 13,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingHorizontal: IS_TABLET ? 16 : 10,
    paddingVertical: IS_TABLET ? 8 : 6,
    borderRadius: 4,
  },
  addButtonText: {
    color: theme.colors.background,
    marginRight: 4,
    fontFamily: 'Roboto',
    fontSize: IS_TABLET ? 14 : 13,
    display: IS_TABLET ? 'flex' : 'none', // Hide text on mobile
  },

  // Drawer styles
  drawer: {
    position: 'absolute',
    left: 0,
    width: DRAWER_WIDTH,
    backgroundColor: theme.colors.background,
    zIndex: 10,
    shadowColor: theme.colors.black,
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 10,
    maxWidth: '85%',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    zIndex: 9,
  },
  mainContent: {
    flex: 1,
    backgroundColor: theme.colors.background,
    position: 'absolute',
    top: 0,
    bottom: 0,
    right: 0,
    width: '100%',
    zIndex: 1,
  },
  calendar: {
    flex: 1,
  },

  // Drawer content styles
  drawerContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: 16,
  },
  drawerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  drawerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: 'Roboto_bold',
    color: theme.colors.text.dark,
  },
  drawerCalendar: {
    marginBottom: 24,
  },
  daysHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    paddingHorizontal: 2,
  },
  dayLetter: {
    fontSize: 12,
    color: theme.colors.text.light,
    textAlign: 'center',
    width: 36,
    fontFamily: 'Roboto',
    fontWeight: '500',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  calendarDay: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 2,
    borderRadius: 18,
  },
  calendarDayText: {
    fontSize: 14,
    fontFamily: 'Roboto',
    color: theme.colors.text.dark,
  },
  todayCell: {
    // No background color for today's cell
  },
  todayCellText: {
    color: theme.colors.text.dark,
    fontFamily: 'Roboto',
    fontWeight: 'bold',
  },
  emptyCell: {
    opacity: 0,
  },
  eventDayText: {
    fontWeight: 'bold',
    fontFamily: 'Roboto_bold',
  },
  todayDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: theme.colors.primary,
    position: 'absolute',
    bottom: 6,
  },
  selectedCell: {
    backgroundColor: theme.colors.primary,
    borderRadius: 18,
  },
  selectedCellText: {
    color: theme.colors.background,
    fontWeight: 'bold',
  },

  // Events list styles for drawer
  eventsContainer: {
    flex: 1,
    marginTop: 16,
  },
  daySection: {
    marginBottom: 16,
  },
  daySectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  daySectionIcon: {
    marginRight: 8,
  },
  daySectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text.dark,
    fontFamily: 'Roboto_bold',
  },
  eventListItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  eventDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  eventListTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text.dark,
    fontFamily: 'Roboto',
  },
  eventListTime: {
    fontSize: 14,
    color: theme.colors.text.light,
    fontFamily: 'Roboto',
  },
  dateRangeText: {
    fontSize: 14,
    color: theme.colors.text.light,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 16,
    fontFamily: 'Roboto',
  },

  // Upcoming events styles (legacy)
  upcomingEventsContainer: {
    flex: 1,
  },
  upcomingEventsTitle: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 16,
    fontFamily: 'Roboto_bold',
    color: theme.colors.text.dark,
  },
  eventItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  eventColor: {
    width: 4,
    borderRadius: 2,
    marginRight: 12,
  },
  eventDetails: {
    flex: 1,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
    fontFamily: 'Roboto_bold',
    color: theme.colors.text.dark,
  },
  eventTime: {
    fontSize: 14,
    color: theme.colors.text.light,
    marginBottom: 4,
    fontFamily: 'Roboto',
  },
  eventDescription: {
    fontSize: 14,
    color: theme.colors.text.light,
    fontFamily: 'Roboto',
  },
  noEventsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  noEventsText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.text.light,
    fontFamily: 'Roboto',
  },

  // Agenda view styles
  agendaContainer: {
    flex: 1,
    padding: 16,
  },
  agendaItem: {
    flexDirection: 'row',
    marginBottom: 20,
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 12,
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  agendaDateSection: {
    alignItems: 'center',
    marginRight: 12,
    width: 40,
  },
  agendaDate: {
    fontSize: 12,
    color: theme.colors.text.light,
    fontFamily: 'Roboto',
  },
  agendaDateNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text.dark,
    fontFamily: 'Roboto_bold',
  },
  agendaEventColor: {
    width: 4,
    borderRadius: 2,
    marginRight: 12,
  },
  agendaEventDetails: {
    flex: 1,
  },
  agendaEventTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
    color: theme.colors.text.dark,
    fontFamily: 'Roboto_bold',
  },
  agendaEventTime: {
    fontSize: 14,
    color: theme.colors.text.light,
    marginBottom: 4,
    fontFamily: 'Roboto',
  },
  agendaEventDescription: {
    fontSize: 14,
    color: theme.colors.text.light,
    fontFamily: 'Roboto',
  },

  // Year view styles
  yearContainer: {
    flex: 1,
    padding: IS_TABLET ? 16 : 12,
    backgroundColor: theme.colors.background,
    // For mobile view, center content
    ...(SCREEN_WIDTH < 500 && {
      alignItems: 'center',
      paddingHorizontal: 16,
    }),
  },
  yearSelectorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  yearSelectorText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text.dark,
    marginHorizontal: 30,
    fontFamily: 'Roboto_bold',
  },
  yearNavigationButton: {
    padding: 10,
    borderRadius: 25,
    backgroundColor: `${theme.colors.primary}10`,
    width: 45,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
  },
  yearGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 4,
  },
  monthCard: {
    backgroundColor: theme.colors.background,
    borderRadius: 10,
    marginBottom: 16,
    padding: IS_TABLET ? 10 : 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    // For mobile view (single column), ensure the card is more square-like
    ...(SCREEN_WIDTH < 500 && {
      aspectRatio: 0.9, // Make it slightly taller than wide
      alignSelf: 'center',
      width: SCREEN_WIDTH - 32, // Full width minus padding
    }),
  },
  selectedMonthCard: {
    borderColor: theme.colors.primary,
    borderWidth: 2,
    backgroundColor: `${theme.colors.primary}05`,
  },
  monthName: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text.dark,
    fontFamily: 'Roboto_bold',
    textAlign: 'center',
    marginBottom: 10,
    paddingVertical: 5,
  },
  selectedMonthName: {
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  monthEventIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: theme.colors.primary,
    marginTop: 8,
  },

  // Mini calendar styles for year view
  miniCalendarContainer: {
    width: '100%',
  },
  miniDaysHeader: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
    paddingHorizontal: 2,
    // For mobile view, ensure alignment with grid
    ...(SCREEN_WIDTH < 500 && {
      justifyContent: 'space-between',
      paddingHorizontal: 2,
    }),
  },
  miniDayLetter: {
    fontSize: 12,
    color: theme.colors.text.light,
    textAlign: 'center',
    width: 20, // Default width
    fontFamily: 'Roboto',
    fontWeight: '500',
    // For mobile view, adjust width dynamically
    ...(SCREEN_WIDTH < 500 && {
      width: Math.floor((SCREEN_WIDTH - 60) / 7), // Calculate based on screen width
    }),
  },
  miniCalendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    alignItems: 'center',
    // For mobile view, ensure better alignment
    ...(SCREEN_WIDTH < 500 && {
      justifyContent: 'space-between',
      paddingHorizontal: 2,
    }),
  },
  miniCalendarDay: {
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 2,
    borderRadius: 10,
  },
  miniCalendarDayText: {
    fontSize: 12,
    fontFamily: 'Roboto',
    color: theme.colors.text.dark,
    textAlign: 'center',
  },
  miniTodayCell: {
    // No background color for today's cell
  },
  miniTodayCellText: {
    color: theme.colors.text.dark,
    fontFamily: 'Roboto',
    fontWeight: 'bold',
  },
  miniEmptyCell: {
    opacity: 0,
  },
  miniEventDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: theme.colors.primary,
    position: 'absolute',
    bottom: 2,
  },

  // View selector modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  viewSelectorContainer: {
    position: 'absolute',
    width: IS_TABLET ? 200 : 180,
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  viewOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: IS_TABLET ? 14 : 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  selectedViewOption: {
    backgroundColor: `${theme.colors.primary}15`,
  },
  viewOptionText: {
    fontSize: IS_TABLET ? 17 : 16,
    color: theme.colors.text.dark,
    fontFamily: 'Roboto',
  },
  selectedViewOptionText: {
    color: theme.colors.primary,
    fontWeight: '600',
    fontFamily: 'Roboto_bold',
  },
});

export { DRAWER_WIDTH, IS_TABLET, SCREEN_WIDTH, SCREEN_HEIGHT };