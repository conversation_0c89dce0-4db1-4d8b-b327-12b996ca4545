import React, { useRef, useState, useEffect } from 'react';
import { StyleSheet, Text, TouchableOpacity, View, Pressable, Button } from 'react-native';
import { AntDesign, Feather, Ionicons } from '@expo/vector-icons';
import { CameraView, useCameraPermissions, useMicrophonePermissions } from 'expo-camera';
import * as FileSystem from 'expo-file-system';
import { BlurView } from 'expo-blur';

const VideoRecordingComp = ({ setUri, backPressed, onRecordingComplete }) => {
  const [facing, setFacing] = useState('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [micPermission, requestMicPermission] = useMicrophonePermissions();
  const [timer, setTimer] = useState(0);
  const [intervalId, setIntervalId] = useState(null);

  const cameraRef = useRef();
  const [mode, setMode] = useState('video');
  const [isRecording, setIsRecording] = useState(false);

  useEffect(() => {
    if (isRecording) {
      const id = setInterval(() => setTimer(prev => prev + 1), 1000); 
      setIntervalId(id);
    } else if (!isRecording && intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
    }
    return () => clearInterval(intervalId); 
  }, [isRecording]);

  useEffect(() => {
    if (!isRecording) {
      setTimer(0);
    }
  }, [isRecording]);

  const handleRecord = async () => {
    if (cameraRef.current) {
      if (isRecording) {
        try {
          cameraRef.current.stopRecording();
          console.log('Recording stopped');
          setIsRecording(false);
        } catch (e) {
          console.log('VideoCameraError:', e);
        }
      } else {
        setIsRecording(true);
        try {
          const video = await cameraRef.current.recordAsync();
          const videoUri = video.uri;
          console.log('Video recorded:', videoUri);
          setIsRecording(false);
    
          const fileInfo = await FileSystem.getInfoAsync(videoUri);
          if (fileInfo.exists) {
            setUri(videoUri); 
            if (onRecordingComplete) {
              onRecordingComplete(); 
            }
          } else {
            console.error('File not ready yet.');
          }
        } catch (e) {
          console.error('Recording error:', e);
          setIsRecording(false);
        }
      }
    }
  };


  const formatTime = (timeInSeconds) => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = timeInSeconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  };


  const rotate = () => {
    setFacing((facing) => (facing === 'back' ? 'front' : 'back'));
  };


  const PermissionDenied = () => (
    <View style={styles.permissionContainer}>
      <Ionicons name="alert-circle-outline" size={60} color="#ff6b6b" />
      <Text style={styles.permissionText}>Camera permission denied</Text>
    </View>
  );

  const CameraPermissionRequest = () => (
    <View style={styles.permissionContainer}>
      <Ionicons name="camera-outline" size={60} color="#4dabf7" />
      <Text style={styles.permissionText}>We need your permission to use the camera</Text>
      <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
        <Text style={styles.permissionButtonText}>Grant Permission</Text>
      </TouchableOpacity>
    </View>
  );

  const MicrophonePermissionRequest = () => (
    <View style={styles.permissionContainer}>
      <Ionicons name="mic-outline" size={60} color="#4dabf7" />
      <Text style={styles.permissionText}>We need your permission to record audio</Text>
      <TouchableOpacity style={styles.permissionButton} onPress={requestMicPermission}>
        <Text style={styles.permissionButtonText}>Grant Permission</Text>
      </TouchableOpacity>
    </View>
  );


  if (!permission || !micPermission) {
    return <PermissionDenied />;
  }

  if (!permission.granted) {
    return <CameraPermissionRequest />;
  }

  if (!micPermission.granted) {
    return <MicrophonePermissionRequest />;
  }

  return (
    <View style={styles.container}>
      <CameraView
        style={styles.camera}
        facing={facing}
        ref={cameraRef}
        mode={mode}
        mute={false}
        videoQuality="4:3"
      >
   
        <TouchableOpacity onPress={backPressed} style={styles.backButton}>
          <BlurView intensity={80} tint="dark" style={styles.blurView}>
            <AntDesign name="arrowleft" size={24} color="white" />
          </BlurView>
        </TouchableOpacity>

    
        {isRecording && (
          <View style={styles.timerContainer}>
            <BlurView intensity={80} tint="dark" style={styles.blurView}>
              <View style={styles.timerContent}>
                <View style={styles.recordingIndicator} />
                <Text style={styles.timerText}>{formatTime(timer)}</Text>
              </View>
            </BlurView>
          </View>
        )}

   
        <View style={styles.controlsContainer}>
          <BlurView intensity={50} tint="dark" style={styles.controlsBlur}>

            {!isRecording && (
              <TouchableOpacity style={styles.flipButton} onPress={rotate}>
                <Ionicons name="camera-reverse-outline" size={30} color="white" />
              </TouchableOpacity>
            )}

  
            <TouchableOpacity 
              style={[
                styles.recordButton,
                isRecording ? styles.recordingActive : null
              ]} 
              onPress={handleRecord}
            >
              {isRecording ? (
                <View style={styles.stopRecordIcon} />
              ) : (
                <View style={styles.startRecordIcon} />
              )}
            </TouchableOpacity>

   
            <View style={styles.placeholderButton} />
          </BlurView>
        </View>
      </CameraView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },

  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    padding: 20,
  },
  permissionText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 30,
    color: '#343a40',
  },
  permissionButton: {
    backgroundColor: '#228be6',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 30,
    elevation: 2,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },

  backButton: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 10,
  },
 
  timerContainer: {
    position: 'absolute',
    top: 50,
    alignSelf: 'center',
    zIndex: 10,
  },
  timerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 15,
  },
  recordingIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#ff4757',
    marginRight: 8,
  },
  timerText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },

  controlsContainer: {
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
  },
  controlsBlur: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    margin: 20,
    borderRadius: 40,
    overflow: 'hidden',
    paddingVertical: 20,
  },
  // Buttons
  flipButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  recordingActive: {
    borderColor: '#ff4757',
  },
  startRecordIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#ff4757',
  },
  stopRecordIcon: {
    width: 30,
    height: 30,
    borderRadius: 4,
    backgroundColor: '#ff4757',
  },
  placeholderButton: {
    width: 50,
    height: 50,
  },

  blurView: {
    borderRadius: 20,
    overflow: 'hidden',
  },
});

export default VideoRecordingComp;