import React, { useState, useEffect, useCallback } from "react";
import { View, Text, TouchableOpacity, Modal, StyleSheet, ActivityIndicator } from "react-native";
import Constants from 'expo-constants';
import { Colors } from "../../constants/colors";


const BASE_URL = Constants.expoConfig?.extra?.baseUrl;

// Enhanced error logger
const logError = (context, error) => {
  const errorMessage = `ERROR [${context}]: ${error.message || error}`;
  
  console.log('\n' + '='.repeat(80));
  console.log(errorMessage);
  console.log('Stack:', error.stack || 'No stack trace available');
  
  if (error.response) {
    console.log('Response data:', error.response.data);
    console.log('Response status:', error.response.status);
  }
  
  if (error.request) {
    console.log('Request:', error.request);
  }
  
  if (error.config) {
    console.log('Request URL:', error.config.url);
    console.log('Request method:', error.config.method);
  }
  
  console.log('='.repeat(80) + '\n');
  
  return errorMessage;
};

const LabSelectionModal = ({
  isVisible,
  onClose,
  onConfirmSelection,
}) => {
  const [labs, setLabs] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [selectedLab, setSelectedLab] = useState(null);
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [errors, setErrors] = useState({});
  const [currentStep, setCurrentStep] = useState('lab'); 
  const [isLoading, setIsLoading] = useState(false);
  

  const fetchLabs = useCallback(() => {
    if (!BASE_URL) {
      const errorMsg = logError('Environment', 'BASE_URL is undefined. Check your environment configuration.');
      setErrors(prev => ({ ...prev, baseUrl: errorMsg }));
      return;
    }
    
    setIsLoading(true);
    console.log(`Fetching labs from: ${BASE_URL}/api/forensic/lab`);
    
    fetch(`${BASE_URL}/api/forensic/lab`)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log('Labs data received:', JSON.stringify(data, null, 2));
        if (data.status === "success") {
          setLabs(data.data);
        } else {
          throw new Error(`API returned error: ${data.message || 'Unknown error'}`);
        }
      })
      .catch((error) => {
        const errorMsg = logError('Fetching Labs', error);
        setErrors(prev => ({ ...prev, labsFetch: errorMsg }));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  // Fetch departments for the selected lab - using useCallback
  const fetchDepartments = useCallback((labId) => {
    if (!labId) {
      const errorMsg = logError('Fetch Departments', 'No lab ID provided');
      setErrors(prev => ({ ...prev, departmentsFetch: errorMsg }));
      return;
    }
    
    setIsLoading(true);
    console.log(`Fetching departments for lab ID ${labId} from: ${BASE_URL}/api/forensic/lab/${labId}/department`);
    
    fetch(`${BASE_URL}/api/forensic/lab/${labId}/department`)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log('Departments data received:', JSON.stringify(data, null, 2));
        if (data.status === "success") {
          setDepartments(data.data);
          setCurrentStep('department');
        } else {
          throw new Error(`API returned error: ${data.message || 'Unknown error'}`);
        }
      })
      .catch((error) => {
        const errorMsg = logError('Fetching Departments', error);
        setErrors(prev => ({ ...prev, departmentsFetch: errorMsg }));
        // Even in case of error, switch to department step to show error
        setCurrentStep('department');
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);
  
  // Prefetch labs when the modal becomes visible
  useEffect(() => {
    if (isVisible) {
      setCurrentStep('lab');
      fetchLabs();
    } else {
      // Reset state when modal is closed
      setSelectedLab(null);
      setSelectedDepartment(null);
      setErrors({});
    }
  }, [isVisible, fetchLabs]);

  // Handle lab selection
  const handleLabSelect = (lab) => {
    console.log('Lab selected:', lab.name, 'ID:', lab._id);
    setSelectedLab(lab);
    fetchDepartments(lab._id);
  };

  // Handle department selection
  const handleDepartmentSelect = (department) => {
    console.log('Department selected:', department.name, 'ID:', department._id);
    setSelectedDepartment(department);
  };

  // Handle confirmation
  const handleConfirm = () => {
    if (!selectedLab || !selectedDepartment) {
      const errorMsg = logError('Confirmation', 'No lab or department selected');
      setErrors(prev => ({ ...prev, confirmation: errorMsg }));
      return;
    }
    
    console.log('Confirming selection - Lab:', selectedLab.name, 'Department:', selectedDepartment.name);
    onConfirmSelection(selectedLab._id, selectedDepartment._id); 
    onClose();
  };
  
  // Go back to lab selection
  const goBackToLabs = () => {
    setCurrentStep('lab');
    setSelectedDepartment(null);
  };

  return (
    <Modal visible={isVisible} animationType="fade" transparent={true}>
      <View style={styles.overlay}>
        <View style={styles.modalView}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {currentStep === 'lab' 
                ? 'Select Lab' 
                : `Select Department for ${selectedLab ? selectedLab.name : ''}`}
            </Text>
          </View>

          {isLoading ? (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size="large" color="#007BFF" />
              <Text style={styles.loaderText}>
                {currentStep === 'lab' ? 'Loading labs...' : 'Loading departments...'}
              </Text>
            </View>
          ) : currentStep === 'lab' ? (
            // Lab Selection View
            labs.length > 0 ? (
              <View style={styles.optionsContainer}>
                {labs.map((lab, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.option}
                    onPress={() => handleLabSelect(lab)}
                  >
                    <View style={styles.optionRow}>
                      <View style={[
                        styles.selectionIndicator, 
                        selectedLab && selectedLab._id === lab._id && styles.selectedIndicator
                      ]}>
                        {selectedLab && selectedLab._id === lab._id && <View style={styles.innerDot} />}
                      </View>
                      <Text style={styles.optionText}>{lab.name}</Text>
                    </View>
                  </TouchableOpacity>
                ))
              }
              </View>
            ) : (
              <Text style={styles.noDataText}>
                {errors.labsFetch ? 'Error loading labs' : 'No labs available'}
              </Text>
            )
          ) : (
            // Department Selection View
            departments.length > 0 ? (
              <View style={styles.optionsContainer}>
                {departments.map((dept, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.option}
                    onPress={() => handleDepartmentSelect(dept)}
                  >
                    <View style={styles.optionRow}>
                      <View style={[
                        styles.selectionIndicator, 
                        selectedDepartment && selectedDepartment._id === dept._id && styles.selectedIndicator
                      ]}>
                        {selectedDepartment && selectedDepartment._id === dept._id && <View style={styles.innerDot} />}
                      </View>
                      <Text style={styles.optionText}>{dept.name}</Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            ) : (
              <Text style={styles.noDataText}>
                {errors.departmentsFetch ? 'Error loading departments' : 'No departments available'}
              </Text>
            )
          )}

          <View style={styles.buttonRow}>
            {currentStep === 'lab' ? (
              <TouchableOpacity
                style={[styles.button, styles.secondaryButton]}
                onPress={onClose}
              >
                <Text style={[styles.buttonText, styles.secondaryButtonText]}>Cancel</Text>
              </TouchableOpacity>
            ) : (
              <>
                <TouchableOpacity
                  style={[styles.button, styles.backButton]}
                  onPress={goBackToLabs}
                >
                  <Text style={[styles.buttonText]}>Back</Text>
                </TouchableOpacity>
                <View style={styles.buttonDivider} />
                <TouchableOpacity
                  style={[
                    styles.button, 
                    styles.primaryButton,
                    !selectedDepartment && styles.disabledButton
                  ]}
                  onPress={handleConfirm}
                  disabled={!selectedDepartment}
                >
                  <Text style={[styles.buttonText, styles.primaryButtonText]}>Confirm</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
  },
  modalView: {
    width: "80%",
    backgroundColor: Colors.background,
    borderRadius: 10,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    paddingBottom: 10,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Roboto_bold',
    textAlign: 'center',
    color: Colors.black,
  },
  optionsContainer: {
    maxHeight: 300,
  },
  option: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectionIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.border,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedIndicator: {
    borderColor: Colors.primary,
  },
  innerDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.primary,
  },
  optionText: {
    fontSize: 16,
    fontFamily: 'Roboto',
    color: Colors.black,
  },
  noDataText: {
    padding: 20,
    textAlign: 'center',
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  button: {
    padding: 12,
    borderRadius: 5,
    flex: 1,
    alignItems: "center",
  },
  primaryButton: {
    backgroundColor: Colors.primary,
  },
  secondaryButton: {
    backgroundColor: Colors.lightText,
  },
  backButton: {
    backgroundColor: Colors.lightText,
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
  },
  buttonText: {
    color: Colors.background,
    fontFamily: 'Roboto_bold',
  },
  secondaryButtonText: {
    color: "white",
  },
  primaryButtonText: {
    color: "white",
  },
  buttonDivider: {
    width: 10,
  },
  loaderContainer: {
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderText: {
    marginTop: 10,
    color: Colors.lightText,
    fontSize: 16,
    fontFamily: 'Roboto',
  },
});

export default LabSelectionModal;