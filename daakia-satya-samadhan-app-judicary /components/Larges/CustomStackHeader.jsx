import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useNavigation } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function CustomStackHeader({
  title,
  showBackButton = true, // Show back button by default
  backIcon = 'chevron-back-outline', // Default custom back icon (you can change this)
  backButtonColor = '#9e9e9e', // Default back button color
  onBackPress, // Custom back press action
  showSearchIcon = true, // Show search icon by default
  searchIconColor = '#9e9e9e', // Default search icon color
  onSearchPress, // Custom search press action
  showNotificationIcon = true, // Show notification icon by default
  notificationIconColor = '#9e9e9e', // Default notification icon color
  onNotificationPress, // Custom notification press action
  showBorder = true, // Show bottom border by default
  borderColor = '#9e9e9e', // Default border color
  backgroundColor = '#fff', // Default background color
}) {
  const navigation = useNavigation();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress(); // Use custom back action if provided
    } else {
      navigation.goBack(); // Default back action
    }
  };

  return (
    <View
      style={[
        styles.headerContainer,
        { backgroundColor, borderBottomWidth: showBorder ? 1 : 0, borderBottomColor: borderColor },
      ]}
    >
      {/* Left Side: Back Button + Title */}
      <View style={styles.leftContainer}>
        {showBackButton && (
          <TouchableOpacity onPress={handleBackPress}>
            <Ionicons name={backIcon} size={26} color={backButtonColor} />
          </TouchableOpacity>
        )}
        <Text style={styles.title}>{title}</Text>
      </View>

      {/* Right Icons (Search + Notifications) */}
      <View style={styles.rightIcons}>
        {showSearchIcon && (
          <TouchableOpacity onPress={onSearchPress}>
            <Ionicons name="search-outline" size={22} color={searchIconColor} />
          </TouchableOpacity>
        )}
        {showNotificationIcon && (
          <TouchableOpacity onPress={onNotificationPress}>
            <Ionicons name="notifications-outline" size={22} color={notificationIconColor} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 25,
    paddingVertical: 20,
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 15,
  },
  rightIcons: {
    flexDirection: 'row',
    gap: 23,
  },
});