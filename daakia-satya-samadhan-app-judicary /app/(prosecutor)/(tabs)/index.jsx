import React, { useState } from "react";
import { View, Text, TouchableOpacity, Image, StyleSheet, TextInput, ScrollView } from "react-native";
import { useRouter } from 'expo-router';
import Constants from 'expo-constants';
import { useAuth } from '../../../context/auth-context';
import FontAwesome from "@expo/vector-icons/FontAwesome";

export default function HomeDispatcher() {
  const router = useRouter();
  const { token } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSearch = () => {
    if (!searchQuery.trim()) return;
    router.push('(prosecutor)/(screensDispatchers)/prosecutorRecentCases');
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.contentContainer}>
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <FontAwesome name="search" size={20} color="#666" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search packages..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={handleSearch}
            />
            {searchQuery !== '' && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <FontAwesome name="times-circle" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>
          <TouchableOpacity 
            style={[styles.searchButton, isLoading && styles.searchButtonDisabled]}
            onPress={handleSearch}
            disabled={isLoading}
          >
            <Text style={styles.searchButtonText}>
              {isLoading ? 'Searching...' : 'Search'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.container}>
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.caseButton} 
              onPress={() => router.push('(prosecutor)/(screensDispatchers)/prosecutorRecentCases')}
            >
              <Image 
                source={require('../../../assets/images/recent_cases.png')} 
                style={styles.buttonIcon} 
              />
              <Text style={styles.buttonText}>Recent Cases</Text>
            </TouchableOpacity>
          </View>
          
          {/* Upcoming Events Section */}
          <View style={styles.eventsOuterContainer}>
            <Text style={styles.eventsTitle}>UPCOMING EVENTS</Text>
            
            <ScrollView style={styles.eventsScrollView} showsVerticalScrollIndicator={false}>
              {/* April 2025 Section */}
              <Text style={styles.monthTitle}>APRIL 2025</Text>
              
              <View style={styles.eventCard}>
                <View style={styles.dateColumn}>
                  <Text style={styles.dateNumber}>12</Text>
                  <Text style={styles.dateDay}>Wed</Text>
                </View>
                <View style={styles.blueBar}></View>
                <View style={styles.eventsColumn}>
                  <View style={styles.eventItem}>
                    <Text style={styles.eventName}>Witness1_Recording</Text>
                    <Text style={styles.eventTime}>12:30 PM</Text>
                  </View>
                  <View style={styles.eventItem}>
                    <Text style={styles.eventName}>Witness1_Recording</Text>
                    <Text style={styles.eventTime}>04:30 PM</Text>
                  </View>
                </View>
              </View>
              
              <View style={styles.eventCard}>
                <View style={styles.dateColumn}>
                  <Text style={styles.dateNumber}>05</Text>
                  <Text style={styles.dateDay}>Fri</Text>
                </View>
                <View style={styles.blueBar}></View>
                <View style={styles.eventsColumn}>
                  <View style={styles.eventItem}>
                    <Text style={styles.eventName}>Witness1_Recording</Text>
                    <Text style={styles.eventTime}>11:45 AM</Text>
                  </View>
                  <View style={styles.eventItem}>
                    <Text style={styles.eventName}>Witness1_Recording</Text>
                    <Text style={styles.eventTime}>09:00 AM</Text>
                  </View>
                </View>
              </View>
              
              {/* March 2025 Section */}
              <Text style={styles.monthTitle}>MAR 2025</Text>
              
              <View style={styles.eventCard}>
                <View style={styles.dateColumn}>
                  <Text style={styles.dateNumber}>24</Text>
                  <Text style={styles.dateDay}>Tues</Text>
                </View>
                <View style={styles.blueBar}></View>
                <View style={styles.eventsColumn}>
                  <View style={styles.eventItem}>
                    <Text style={styles.eventName}>Witness1_Recording</Text>
                    <Text style={styles.eventTime}>12:30 PM</Text>
                  </View>
                  <View style={styles.eventItem}>
                    <Text style={styles.eventName}>Witness1_Recording</Text>
                    <Text style={styles.eventTime}>04:30 PM</Text>
                  </View>
                  <View style={styles.eventItem}>
                    <Text style={styles.eventName}>Witness1_Recording</Text>
                    <Text style={styles.eventTime}>04:30 PM</Text>
                  </View>
                </View>
              </View>
            </ScrollView>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: "#fff",
  },
  contentContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    alignItems: "center",
    backgroundColor: "#fff",
    padding: 30,
  },
  searchContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    padding: 20,
    backgroundColor: '#fff',
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  searchButton: {
    backgroundColor: '#0B36A1',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  searchButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Roboto',
    fontSize: 16,
  },
  searchButtonDisabled: {
    backgroundColor: '#cccccc',
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "center",
    gap: 30,
    width: "100%",
    marginBottom: 30,
  },
  caseButton: {
    width: 140,
    height: 140,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 3,
    borderColor: "#d0d9f5",
    borderRadius: 16,
    borderStyle: "dashed",
    backgroundColor: "#fff",
  },
  buttonIcon: {
    width: 40,
    height: 40,
    resizeMode: "contain",
    tintColor: "#0B36A1",
  },
  buttonText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: "bold",
    color: "#0B36A1",
    textAlign: "center",
    fontFamily: "Roboto_bold",
  },
  // Upcoming Events Styles
  eventsOuterContainer: {
    width: '100%',
    marginTop: 60,
  },
  eventsScrollView: {
    maxHeight: 400,
  },
  eventsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  monthTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginVertical: 15,
  },
  eventCard: {
    width: '100%',
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  dateColumn: {
    width: 50,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
  },
  dateNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  dateDay: {
    fontSize: 14,
    color: '#666',
  },
  blueBar: {
    width: 5,
    backgroundColor: '#0B36A1',
  },
  eventsColumn: {
    flex: 1,
    paddingVertical: 5,
  },
  eventItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    backgroundColor: '#f8f9fc',
    marginVertical: 5,
    marginHorizontal: 10,
    borderRadius: 4,
  },
  eventName: {
    fontSize: 16,
    color: '#333',
  },
  eventTime: {
    fontSize: 14,
    color: '#666',
  },
});