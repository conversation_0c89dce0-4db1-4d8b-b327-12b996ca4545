import { View, Text, StyleSheet, TouchableOpacity, ScrollView, StatusBar, SafeAreaView, Switch } from 'react-native';
import React, { useState } from 'react';
import { useAuth } from '../../../context/auth-context';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../../constants/colors';
import { LinearGradient } from 'expo-linear-gradient';
import { useEASUpdate } from '../../../hooks/useEASUpdate';

const SettingsDispatcher = () => {
  const { logout } = useAuth();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [darkModeEnabled, setDarkModeEnabled] = useState(false);
  const [faceIdEnabled, setFaceIdEnabled] = useState(false);
  const { checkForUpdates, isChecking } = useEASUpdate();

  const handleLogout = () => {
    logout();
  };

  const handleCheckUpdates = () => {
    checkForUpdates();
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar style="light" />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* App Settings Section */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeaderRow}>
            <Ionicons name="settings-outline" size={24} color={Colors.primary} />
            <Text style={styles.sectionTitle}>App Settings</Text>
          </View>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="notifications-outline" size={22} color={Colors.primary} style={styles.settingIcon} />
              <View>
                <Text style={styles.settingText}>Notifications</Text>
                <Text style={styles.settingDescription}>Get alerts about updates</Text>
              </View>
            </View>
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              trackColor={{ false: Colors.border, true: `${Colors.primary}80` }}
              thumbColor={notificationsEnabled ? Colors.primary : "#f4f3f4"}
            />
          </View>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="scan-outline" size={22} color={Colors.primary} style={styles.settingIcon} />
              <View>
                <Text style={styles.settingText}>Face Recognition</Text>
                <Text style={styles.settingDescription}>Quick and secure login</Text>
              </View>
            </View>
            <Switch
              value={faceIdEnabled}
              onValueChange={setFaceIdEnabled}
              trackColor={{ false: Colors.border, true: `${Colors.primary}80` }}
              thumbColor={faceIdEnabled ? Colors.primary : "#f4f3f4"}
            />
          </View>

          <TouchableOpacity style={styles.settingItem} onPress={handleCheckUpdates}>
            <View style={styles.settingInfo}>
              <Ionicons name="refresh-outline" size={22} color={Colors.primary} style={styles.settingIcon} />
              <View>
                <Text style={styles.settingText}>Check for Updates</Text>
                <Text style={styles.settingDescription}>Get the latest version</Text>
              </View>
            </View>
            {isChecking ? (
              <Ionicons name="sync" size={22} color={Colors.primary} />
            ) : (
              <Ionicons name="chevron-forward" size={22} color={Colors.lightText} />
            )}
          </TouchableOpacity>
        </View>

        {/* More Options Section */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeaderRow}>
            <Ionicons name="apps-outline" size={24} color={Colors.primary} />
            <Text style={styles.sectionTitle}>More Options</Text>
          </View>
          
          <TouchableOpacity style={styles.optionItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="shield-checkmark-outline" size={22} color={Colors.primary} style={styles.settingIcon} />
              <View>
                <Text style={styles.settingText}>Privacy Policy</Text>
                <Text style={styles.settingDescription}>Read our privacy policy</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={22} color={Colors.lightText} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.optionItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="document-text-outline" size={22} color={Colors.primary} style={styles.settingIcon} />
              <View>
                <Text style={styles.settingText}>Terms of Service</Text>
                <Text style={styles.settingDescription}>View terms and conditions</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={22} color={Colors.lightText} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.optionItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="help-circle-outline" size={22} color={Colors.primary} style={styles.settingIcon} />
              <View>
                <Text style={styles.settingText}>Help & Support</Text>
                <Text style={styles.settingDescription}>Get assistance</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={22} color={Colors.lightText} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.optionItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="information-circle-outline" size={22} color={Colors.primary} style={styles.settingIcon} />
              <View>
                <Text style={styles.settingText}>About</Text>
                <Text style={styles.settingDescription}>App information</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={22} color={Colors.lightText} />
          </TouchableOpacity>
        </View>

        {/* Logout Button */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <LinearGradient
            colors={['#d9534f', '#c9302c']}
            style={styles.logoutGradient}
          >
            <Ionicons name="log-out-outline" size={22} color="#fff" style={styles.logoutIcon} />
            <Text style={styles.logoutText}>Logout</Text>
          </LinearGradient>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContainer: {
    flexGrow: 1,
  },
  header: {
    padding: 20,
    paddingTop: 40,
    paddingBottom: 30,
  },
  headerTitle: {
    fontSize: 24,
    color: '#fff',
    fontFamily: 'Roboto_bold',
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.8)',
    fontFamily: 'Roboto',
    marginTop: 5,
  },
  sectionContainer: {
    backgroundColor: Colors.background,
    marginHorizontal: 20,
    marginTop: 20,
    borderRadius: 15,
    padding: 20,
    elevation: 2,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    color: Colors.primary,
    fontFamily: 'Roboto_bold',
    marginLeft: 10,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 0.2,
    borderBottomColor: Colors.border,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 0.2,
    borderBottomColor: Colors.border,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 15,
    width: 24,
  },
  settingText: {
    fontSize: 15,
    color: Colors.black,
    fontFamily: 'Roboto',
  },
  settingDescription: {
    fontSize: 12,
    color: Colors.lightText,
    fontFamily: 'Roboto',
    marginTop: 2,
  },
  logoutButton: {
    marginHorizontal: 20,
    marginTop: 30,
    marginBottom: 30,
    borderRadius: 12,
    overflow: 'hidden',
  },
  logoutGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
  },
  logoutIcon: {
    marginRight: 10,
  },
  logoutText: {
    color: '#fff',
    fontSize: 15,
    fontFamily: 'Roboto_bold',
  },
});

export default SettingsDispatcher;