import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Image, 
  TouchableOpacity, 
  ScrollView,
  StatusBar,
  SafeAreaView,
  useWindowDimensions,
  ActivityIndicator,
} from 'react-native';
import { useRouter, useFocusEffect } from 'expo-router'; 
import { useAuth } from '../../../context/auth-context';
import { transformUrl } from '../../../utils/transformUrl';
import { apiService } from '../../../services/api';



const ProsecutorRecentCases = () => {
  const { token } = useAuth();
  const [cases, setCases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();
  const { width } = useWindowDimensions();
  
  // Determine items per row based on screen width
  const getItemsPerRow = (screenWidth) => {
    if (screenWidth >= 1600) return 6;
    if (screenWidth >= 1300) return 5;
    if (screenWidth >= 1000) return 4;
    if (screenWidth >= 700) return 3;
    return 2; 
  };
  const itemsPerRow = getItemsPerRow(width);
  const HORIZONTAL_PADDING = 16;
  const ITEM_MARGIN = 8;
  const THUMBNAIL_SIZE = (width - (HORIZONTAL_PADDING * 2) - (ITEM_MARGIN * 2 * itemsPerRow)) / itemsPerRow;

  // Fetch data whenever the screen is focused
  useFocusEffect(
    React.useCallback(() => {
      fetchRecentCases();
    }, [token])
  );

  const fetchRecentCases = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiService.fetchDispatcherRecentCases(token);
      
      if (response.status === 'success' && response.data && response.data.cases) {
        setCases(response.data.cases);
      } else {
        setError('Failed to fetch cases data');
      }
    } catch (err) {
      console.error('API Error:', err.message);
      setError('An error occurred while fetching cases');
    } finally {
      setLoading(false);
    }
  };

  const segregateEvidences = (caseItem) => {
    const forensicRequestId = caseItem._id;
    console.log(forensicRequestId);
    const segregatedEvidences = {};
    
    caseItem.evidences.forEach(evidence => {
      const labId = evidence.labId._id;
      const labDepartmentId = evidence.labDepartmentId._id;
      
      if (!segregatedEvidences[labId]) {
        segregatedEvidences[labId] = {
          name: evidence.labId.name,
          departments: {}
        };
      }
      
      if (!segregatedEvidences[labId].departments[labDepartmentId]) {
        segregatedEvidences[labId].departments[labDepartmentId] = {
          name: evidence.labDepartmentId.name,
          evidences: []
        };
      }
      
      segregatedEvidences[labId].departments[labDepartmentId].evidences.push({
        ...evidence,
        status: caseItem.status,
        forensicRequestId
      });
    });
    
    return segregatedEvidences;
  };

  const handlePackagePress = (caseItem) => {
    const caseData = {
      forensicRequestId: caseItem._id,
      caseId: caseItem.caseId._id,
      caseTitle: caseItem.caseId.title,
      caseDescription: caseItem.caseId.description,
      status: caseItem.status,
      segregatedEvidences: segregateEvidences(caseItem)
    };
    
    router.push({
      pathname: '/ForensicRequestDetails',
      params: { caseData: JSON.stringify(caseData) },
    });
  };

  const renderPackageItem = (item) => {
    const packageImageUrl = item.caseId.packageImageUrl && item.caseId.packageImageUrl.length > 0 
      ? item.caseId.packageImageUrl[0] 
      : null;
    
    return (
      <TouchableOpacity 
        key={item._id}
        style={[styles.packageItem, { 
          width: THUMBNAIL_SIZE, 
          height: THUMBNAIL_SIZE + 50,
          margin: ITEM_MARGIN
        }]}
        onPress={() => handlePackagePress(item)}
        testID={`package-item-${item._id}`}
      >
        {packageImageUrl ? (
          <Image
            source={{ uri: transformUrl(packageImageUrl) }}
            style={styles.packageImage}
            resizeMode="cover"
          />
        ) : (
          <View style={[styles.packageImage, styles.noImageContainer]}>
            <Text style={styles.noImageText}>No Image</Text>
          </View>
        )}
        <View style={styles.packageLabelContainer}>
          <Text style={styles.packageLabel} numberOfLines={2} ellipsizeMode="tail">
            {item.caseId.title || "Untitled Case"}
          </Text>
          <Text style={styles.packageMeta}>
            {`${item.evidences.length} Evidence${item.evidences.length !== 1 ? 's' : ''}`}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color="#0a34a1" />
          <Text style={styles.loadingText}>Loading evidence packages...</Text>
        </View>
      );
    }
    
    if (error) {
      return (
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchRecentCases}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }
    
    if (cases.length === 0) {
      return (
        <View style={styles.centerContainer}>
          <Text style={styles.emptyText}>No evidence packages available</Text>
        </View>
      );
    }
    
    return (
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}
      >
        <View style={styles.headerContainer}>
          <Text style={styles.sectionTitle}>
            Evidence Packages ({cases.length})
          </Text>
          <TouchableOpacity 
            style={styles.refreshButton} 
            onPress={fetchRecentCases}
            testID="refresh-button"
          >
            <Text style={styles.refreshButtonText}>Refresh</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.gridContainer}>
          {cases.map(item => renderPackageItem(item))}
        </View>
      </ScrollView>
    );
  };

  return (
    <SafeAreaView style={styles.safeAreaContainer}>
      <StatusBar style="dark" />
      <View style={styles.container}>
        {renderContent()}
      </View>
    </SafeAreaView>
  );
};



// Styles remain unchanged
const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#0a34a1',
  },
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    fontFamily: 'Roboto_bold',
  },
  refreshButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#0a34a1',
    borderRadius: 4,
    fontFamily: 'Roboto_bold',
  },
  refreshButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Roboto_bold',
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  packageItem: {
    borderRadius: 10,
    backgroundColor: '#fff',
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  packageImage: {
    width: '100%',
    height: '70%',
  },
  noImageContainer: {
    backgroundColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noImageText: {
    color: '#757575',
    fontSize: 14,
  },
  packageLabelContainer: {
    padding: 10,
    backgroundColor: '#fff',
    height: '30%',
    justifyContent: 'space-between',
  },
  packageLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    fontFamily: 'Roboto',
  },
  packageMeta: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
    fontFamily: 'Roboto',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    fontFamily: 'Roboto',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
    fontFamily: 'Roboto',
  },
  errorText: {
    fontSize: 16,
    color: '#d32f2f',
    textAlign: 'center',
    marginBottom: 16,
    fontFamily: 'Roboto',
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#0a34a1',
    borderRadius: 4,
    fontFamily: 'Roboto_bold',
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Roboto_bold',
  },
});

export default ProsecutorRecentCases;