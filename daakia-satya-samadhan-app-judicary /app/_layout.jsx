import React, { useEffect } from 'react';
import { Text } from 'react-native';
import { Slot } from 'expo-router';
import { AuthProvider } from '../context/auth-context';
import * as SplashScreen from 'expo-splash-screen';
import { useFonts } from 'expo-font';


SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    'Roboto': require('../assets/fonts/Roboto-Regular.ttf'),
    'Roboto_bold': require('../assets/fonts/Roboto-Bold.ttf'),
    'SpaceMono': require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded]);

  if (!fontsLoaded) {
    return null;
  }

 
  Text.defaultProps = Text.defaultProps || {};
  Text.defaultProps.style = {
    ...(Text.defaultProps?.style || {}),
    fontFamily: 'Roboto', 
  };

  return (
    <AuthProvider>
      <Slot />
    </AuthProvider>
  );
}
