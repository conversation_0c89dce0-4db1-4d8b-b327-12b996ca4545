import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import Tab1 from './caseDetailsTabs/casetab1';
import Tab2 from './caseDetailsTabs/casetab2';
import Tab3 from './caseDetailsTabs/casetab3';
import { Colors } from '../../../constants/colors';

const CaseDetails = () => {
  const { caseid } = useLocalSearchParams();
  const [activeTab, setActiveTab] = useState('tab1');


  const changeTab = (tabName) => {
    setActiveTab(tabName);
  };

  // Render the content for the active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'tab1':
        return <Tab1 caseid={caseid} changeTab={changeTab}  />;
      case 'tab2':
        return <Tab2 caseid={caseid} changeTab={changeTab}  />;
      case 'tab3':
        return <Tab3 caseid={caseid} />;
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Custom Tab Bar */}
      <View style={styles.tabBar}>
        <TouchableOpacity
          style={[styles.tabItem, activeTab === 'tab1' && styles.activeTab]}
          onPress={() => setActiveTab('tab1')}
        >
          <Text style={[styles.tabText, activeTab === 'tab1' && styles.activeTabText]}>
            Police Officer
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabItem, activeTab === 'tab2' && styles.activeTab]}
          onPress={() => setActiveTab('tab2')}
        >
          <Text style={[styles.tabText, activeTab === 'tab2' && styles.activeTabText]}>
          Forensic Dept
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabItem, activeTab === 'tab3' && styles.activeTab]}
          onPress={() => setActiveTab('tab3')}
        >
          <Text style={[styles.tabText, activeTab === 'tab3' && styles.activeTabText]}>
          Judiciary
          </Text>
        </TouchableOpacity>
      </View>

      {/* Tab Content */}
      <View style={styles.tabContent}>
        {renderContent()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  tabBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  tabItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.primary,
  },
  tabText: {
    fontSize: 13,
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
  activeTabText: {
    color: Colors.primary,
    fontWeight: 'bold',
    fontFamily: 'Roboto_bold',
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
});

export default CaseDetails;