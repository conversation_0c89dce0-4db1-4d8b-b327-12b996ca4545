import React, { useState } from "react";
import { View, Text, TouchableOpacity, Image, StyleSheet, Modal, Alert, TextInput, ScrollView } from "react-native";
import QRScanner from "../../../../components/Larges/QRScanner";
import { useRouter } from 'expo-router'; 
import Constants from 'expo-constants';
import FontAwesome from "@expo/vector-icons/FontAwesome";
import { apiService } from "../../../../services/api";
import { useAuth } from "../../../../context/auth-context";

export default function JudgeHome() {
  const router = useRouter(); 
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [scannedData, setScannedData] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { token } = useAuth();
  const BASE_URL = Constants.expoConfig?.extra?.baseUrl;

  console.log(BASE_URL);

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    try {
      setIsLoading(true);
      const response = await apiService.searchCaseByFIR(token, searchQuery.trim());
      console.log("Search Response:", response);
      
      if (response.data && response.data.length > 0) {
        const caseId = response.data[0]._id;
        router.push({
          pathname: '/(officer)/(screens)/caseDetails',
          params: { caseid: caseId }
        });
      } else {
        Alert.alert(
          "No Case Found",
          "No case found with the given FIR number",
          [{ text: "OK", onPress: () => console.log("OK Pressed") }],
          { cancelable: false }
        );
      }
    } catch (error) {
      console.error("Search error:", error);
      Alert.alert(
        "Search Failed",
        "Failed to search for the case. Please try again.",
        [{ text: "OK", onPress: () => console.log("OK Pressed") }],
        { cancelable: false }
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleScanComplete = (data) => {
    setScannedData(data);
    setIsModalVisible(false);
    
    try {
      const url = new URL(data);
      const path = url.pathname;
      
      if (path.includes('/cases/') && path.includes('/evidences/')) {
        const segments = path.split('/');
        const caseid = segments[2]; 
        const evidenceId = segments[4]; 
        
        router.push({
          pathname: '/(officer)/(screens)/evidenceDetails',
          params: { caseid, evidenceId }
        });
      } 
      else if (path.includes('/cases/') && !path.includes('/evidences/')) {
        const segments = path.split('/');
        const caseid = segments[2]; 
        
        router.push({
          pathname: '/(officer)/(screens)/caseDetails',
          params: { caseid }
        });
      }
      else if (path.includes('/forensicRequests/')) {
        Alert.alert(
          "Incorrect Scanner",
          "Please use the Dispatcher scanner for forensic requests",
          [{ text: "OK", onPress: () => console.log("OK Pressed") }],
          { cancelable: false }
        );
      }
      else {
        Alert.alert(
          "Invalid QR Code",
          "The scanned QR code is not in a recognized format",
          [{ text: "OK", onPress: () => console.log("OK Pressed") }],
          { cancelable: false }
        );
      }
    } catch (error) {
      console.error("Error parsing URL:", error);
      Alert.alert(
        "Invalid QR Code",
        "Could not process the scanned QR code",
        [{ text: "OK", onPress: () => console.log("OK Pressed") }],
        { cancelable: false }
      );
    }
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.contentContainer}>
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <FontAwesome name="search" size={20} color="#666" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search by FIR number..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={handleSearch}
              keyboardType="numeric"
              maxLength={10}
            />
            {searchQuery !== '' && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <FontAwesome name="times-circle" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>
          <TouchableOpacity 
            style={[styles.searchButton, isLoading && styles.searchButtonDisabled]}
            onPress={handleSearch}
            disabled={isLoading}
          >
            <Text style={styles.searchButtonText}>
              {isLoading ? 'Searching...' : 'Search'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.scrollContent}>
          <View style={styles.container}>
            <View style={styles.buttonContainer}>
              <TouchableOpacity style={styles.caseButton} onPress={() => router.push('/(officer)/(screens)/recentCases')}>
                <Image source={require('../../../../assets/images/recent_cases.png')} style={styles.buttonIcon} />
                <Text style={styles.buttonText}>Recent Cases</Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity style={styles.qrSection} onPress={() => setIsModalVisible(true)}>
              <Text style={styles.qrTitle}>View a case</Text>
              <Text style={styles.qrSubtitle}>Scan QR to view any case</Text>
              <Image source={require('../../../../assets/images/image.png')} style={styles.qrImage} />
            </TouchableOpacity>
          </View>
        </ScrollView>

        <Modal visible={isModalVisible} animationType="slide">
          <QRScanner onScanComplete={handleScanComplete} onClose={handleCloseModal} />
        </Modal>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: "#fff",
  },
  contentContainer: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    alignItems: "center",
    padding: 30,
  },
  searchContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    padding: 20,
    backgroundColor: '#fff',
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  searchButton: {
    backgroundColor: '#0B36A1',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  searchButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Roboto',
    fontSize: 16,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "center",
    gap: 30,
    width: "100%",
    marginBottom: 50,
  },
  caseButton: {
    width: 140,
    height: 140,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 3,
    borderColor: "#d0d9f5",
    borderRadius: 16,
    borderStyle: "dashed",
    backgroundColor: "#fff",
  },
  buttonIcon: {
    width: 40,
    height: 40,
    resizeMode: "contain",
    tintColor: "#0B36A1",
  },
  buttonText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: "bold",
    color: "#0B36A1",
    textAlign: "center",
    fontFamily: 'Roboto_bold',
  },
  qrSection: {
    alignItems: "center",
    marginVertical: 30,
  },
  qrTitle: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#0B36A1",
    fontFamily: 'Roboto_bold',
  },
  qrSubtitle: {
    fontSize: 16,
    color: "gray",
    marginBottom: 15,
    fontFamily: 'Roboto',
  },
  qrImage: {
    width: 180,
    height: 180,
    resizeMode: "contain",
    marginLeft: 13,
  },
  logoutContainer: {
    position: "absolute",
    bottom: 50,
    alignItems: "center",
  },
  userText: {
    fontSize: 18,
    marginBottom: 12,
    fontWeight: "bold",
    fontFamily: 'Roboto',
  },
  searchButtonDisabled: {
    backgroundColor: '#cccccc',
  },
});