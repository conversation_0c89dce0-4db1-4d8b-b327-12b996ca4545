# <PERSON><PERSON>a <PERSON> Judiciary App

This is the judiciary application for the <PERSON><PERSON><PERSON> platform designed for judicial officials.

## Technologies

- React Native with Expo
- Expo Router for navigation
- Secure authentication with JWT
- Document viewer and management
- Case tracking and management
- Local authentication
- File sharing and printing

## Prerequisites

- Node.js 16+
- npm or yarn
- Expo CLI
- iOS/Android development environment
- Expo Go app (for testing)

## Installation

1. Clone the repository
```bash
git clone [repository-url]
cd daakia-satya-samadhan-app-judicary
```

2. Install dependencies
```bash
npm install
```

3. Set up environment variables
Create appropriate `.env` files for different environments:
- `.env.development`
- `.env.preview`
- `.env.production`

## Running the App

```bash
# Start the development server
npx expo start

# Run on Android
npx expo start --android

# Run on iOS
npx expo start --ios

# Run on web
npx expo start --web
```

## Development with Custom Dev Build

For development with custom native modules, you'll need to create a development build:

### Prebuild Step (Required for Dev Builds)

Before creating a development build, you need to generate native code using prebuild:

```bash
# Generate native code for both platforms
npx expo prebuild --clean (clean is optional make sure if you do make the app run more then android 10)

# Or generate for specific platform
npx expo prebuild --platform ios
npx expo prebuild --platform android
```

### Creating Development Builds

```bash
# Create development build for Android
eas build --profile development --platform android

# Create development build for iOS
eas build --profile development --platform ios
```

### Running with Development Client

Once you have installed the development build on your device:

1. Start the development server:
```bash
npx expo start --dev-client
```

2. Open the development build app on your device
3. Scan the QR code from the terminal or enter the server URL manually

## Key Features

- Case management and tracking
- Document viewer and annotations
- Hearing scheduler and notifications
- Secure access to sensitive case information
- E-signing capability for orders and judgments

## Building for Production

This app uses EAS (Expo Application Services) for building. Refer to the `eas.json` file for build profiles.

```bash
# Build for development
eas build --profile development

# Build for preview
eas build --profile preview

# Build for production
eas build --profile production
```

## Project Structure

```
daakia-satya-samadhan-app-judicary/
├── app/                  # Main application screens using Expo Router
├── assets/               # Images, fonts and other static assets
├── components/           # Reusable UI components
├── constants/            # Configuration constants and theme settings
├── context/              # React Context for state management
├── hooks/                # Custom React hooks
├── services/             # API services and backend integration
└── utils/                # Utility functions
```

## Dependencies

The app uses numerous Expo and React Native libraries including:
- Authentication: JWT, AsyncStorage, SecureStore
- UI: Vector Icons, Lottie animations, Blur effects
- Document handling: PDF viewer, document signing
- Navigation: Expo Router, React Navigation
- Sharing: Sharing, Printing

For a complete list of dependencies, refer to the `package.json` file.