import { useState, useEffect } from 'react';
import * as Updates from 'expo-updates';
import { Alert, Platform } from 'react-native';
import Constants from 'expo-constants';

export const useEASUpdate = () => {
  const [isChecking, setIsChecking] = useState(false);
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false);
  const [updateMessage, setUpdateMessage] = useState('');

  const checkForUpdates = async () => {
    try {
      setIsChecking(true);
      
      // Check if running in Expo Go
      if (Constants.appOwnership === 'expo') {
        Alert.alert(
          'Development Mode',
          'Update checking is not available in Expo Go. Please build a development or production build to test updates.',
          [{ text: 'OK' }]
        );
        return;
      }
      
      console.log('Checking for updates...');
      const update = await Updates.checkForUpdateAsync();
      console.log('Update check result:', update);
      
      if (update.isAvailable) {
        setIsUpdateAvailable(true);
        const customMessage = `🆕 New Update Available!

What's New:
• Enhanced user interface
• Faster response times
• Bug fixes and improvements

For more details visit:
https://satya-samadhan.com/updates`;

        setUpdateMessage(customMessage);
        showUpdateAlert(customMessage);
      } else {
        setIsUpdateAvailable(false);
        setUpdateMessage('');
        Alert.alert(
          'No Updates',
          'You are using the latest version of the app!',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error checking for updates:', error);
      Alert.alert(
        'Update Check Failed',
        `Could not check for updates: ${error.message}`,
        [{ text: 'OK' }]
      );
    } finally {
      setIsChecking(false);
    }
  };

  const showUpdateAlert = (message) => {
    Alert.alert(
      'Update Available',
      message,
      [
        {
          text: 'Update Now',
          onPress: async () => {
            try {
              Alert.alert('Downloading', 'Downloading update...');
              await Updates.fetchUpdateAsync();
              Alert.alert(
                'Update Downloaded',
                'The app will now restart to apply the update.',
                [
                  {
                    text: 'OK',
                    onPress: () => Updates.reloadAsync()
                  }
                ]
              );
            } catch (error) {
              console.error('Error updating:', error);
              Alert.alert('Error', 'Failed to update the app. Please try again later.');
            }
          },
        },
        {
          text: 'Later',
          style: 'cancel',
        },
      ],
      { cancelable: false }
    );
  };

  return {
    isChecking,
    isUpdateAvailable,
    updateMessage,
    checkForUpdates,
  };
};