import { useState } from 'react';
import { apiService } from '../services/api';
import { AUTH_ERRORS } from '../constants/auth';

const useUploadMedia = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState(null);

  const uploadMedia = async (uri, type) => {
    setIsUploading(true);
    setError(null);

    try {
      const fileUrl = await apiService.uploadFile(uri, type);
      console.log('File uploaded successfully:', fileUrl);
      return fileUrl;
    } catch (error) {
      setError(error.message);
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  return { uploadMedia, isUploading, error };
};

export default useUploadMedia;