import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet } from 'react-native';

const FormSection = ({ description, onDescriptionChange, onSubmit }) => {
    return (
      <View style={styles.formSection}>
        <View style={styles.formField}>
          <Text style={styles.fieldLabel}>
            Description Here <Text style={styles.requiredAsterisk}>*</Text>
          </Text>
          <TextInput
            style={styles.descriptionInput}
            placeholder="Describe the detail..."
            multiline
            numberOfLines={4}
            value={description}
            onChangeText={onDescriptionChange}
          />
        </View>
        <View>
          <TouchableOpacity style={styles.submitButton} onPress={onSubmit}>
            <Text style={styles.submitButtonText}>SUBMIT</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

const styles = StyleSheet.create({
  formSection: {
    padding: 20,
  },
  formField: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#333',
  },
  requiredAsterisk: {
    color: 'red',
  },
  descriptionInput: {
    borderWidth: 1,
    borderColor: '#e5e5e5',
    borderRadius: 8,
    padding: 12,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#1E40AF',
    padding: 15,
    borderRadius: 15,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default FormSection;