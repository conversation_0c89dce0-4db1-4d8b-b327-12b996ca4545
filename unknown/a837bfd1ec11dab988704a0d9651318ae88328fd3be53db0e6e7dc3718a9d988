# <PERSON><PERSON><PERSON> Forensic App

This is the forensic application for the <PERSON><PERSON><PERSON> platform designed for forensic experts.

## Technologies

- React Native with Expo
- Expo Router for navigation
- Camera and document scanning features
- Image manipulation and analysis
- Secure authentication with JWT
- Local data storage
- File sharing and printing

## Prerequisites

- Node.js 16+
- npm or yarn
- Expo CLI
- iOS/Android development environment
- Expo Go app (for testing)

## Installation

1. Clone the repository
```bash
git clone [repository-url]
cd daakia-satya-samadhan-app-forensic
```

2. Install dependencies
```bash
npm install
```

3. Set up environment variables
Create appropriate `.env` files for different environments:
- `.env.development`
- `.env.preview`
- `.env.production`

## Running the App

```bash
# Start the development server
npx expo start

# Run on Android
npx expo run:android

# Run on iOS
npx expo run:ios

# Run on web
npx expo start --web
```

## Development with Custom Dev Build

This app uses `expo-dev-client` for development with custom native modules. The app already has native code directories (`android/` and `ios/`).

### Creating Development Builds

If you need to create a new development build:

```bash
# Create development build for Android
eas build --profile development --platform android

# Create development build for iOS
eas build --profile development --platform ios
```

### Running with Development Client

Once you have installed the development build on your device:

1. Start the development server:
```bash
npx expo start --dev-client
```

2. Open the development build app on your device
3. Scan the QR code from the terminal or enter the server URL manually

## Key Features

- Evidence collection with camera integration
- Document scanning and processing
- Image analysis and manipulation
- Secure chain of custody tracking
- Report generation and sharing
- Location tagging for evidence

## Building for Production

This app uses EAS (Expo Application Services) for building. Refer to the `eas.json` file for build profiles.

```bash
# Build for development
eas build --profile development

# Build for preview
eas build --profile preview

# Build for production
eas build --profile production
```

## Project Structure

```
daakia-satya-samadhan-app-forensic/
├── android/               # Native Android code
├── ios/                   # Native iOS code
├── app/                   # Main application screens using Expo Router
├── assets/                # Images, fonts and other static assets
├── components/            # Reusable UI components
├── constants/             # Configuration constants and theme settings
├── context/               # React Context for state management
├── hooks/                 # Custom React hooks
├── Service/               # API services and backend integration
└── utils/                 # Utility functions
```

## Native Dependencies

This app uses several native dependencies that require development builds:

- `react-native-vision-camera`: Advanced camera features
- `react-native-document-scanner-plugin`: Document scanning capabilities 
- Other Expo modules requiring native code

To see all dependencies, check the `package.json` file. 