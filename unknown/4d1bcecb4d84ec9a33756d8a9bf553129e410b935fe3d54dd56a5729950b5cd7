import React, { useState } from "react";
import { View, Text, TouchableOpacity, Image, StyleSheet, Modal, Alert } from "react-native";
import { useRouter } from 'expo-router'; 
import { useAuth } from "../../../../context/auth-context";
import QRScanner from "../../../../components/Larges/QRScanner"
import { Colors } from "../../../../constants/colors";


export default function HomeForensic() {
 const router = useRouter(); 
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [scannedData, setScannedData] = useState(null);
 const {token} = useAuth();
 console.log(token)

 const handleScanComplete = (data) => {
  try {
    // Parse the URL
    const url = new URL(data);
    const path = url.pathname;
    

    if (path.includes('/forensicRequests/')) {
      // Extract forensic request ID
      const forensicRequestId = path.split('/')[2];
      

      setIsModalVisible(false);
      
      router.push({
        pathname: '(forensic)/caseDetails',
        params: { forensicRequestId },
      });
    } else {
      // Not a forensic request URL
      setIsModalVisible(false);
      Alert.alert(
        "Invalid QR Code",
        "Please scan a valid forensic request QR code",
        [{ text: "OK" }],
        { cancelable: false }
      );
    }
  } catch (error) {
    // Handle parsing errors
    console.error("Error parsing URL:", error);
    setIsModalVisible(false);
    Alert.alert(
      "Invalid QR Code",
      "Could not process the scanned QR code",
      [{ text: "OK" }],
      { cancelable: false }
    );
  }
};

  const handleCloseModal = () => {
    setIsModalVisible(false);
  };


  return (
    <View style={styles.container}>
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.caseButton} onPress={() => router.push('(forensic)/(screens)/recentCases')}>
          <Image source={require('../../../../assets/images/recent_cases.png')} style={styles.buttonIcon} />
          <Text style={styles.buttonText}>Recent Cases</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity style={styles.qrSection} onPress={() => setIsModalVisible(true)}>
        <Text style={styles.qrTitle}>View a case</Text>
        <Text style={styles.qrSubtitle}>Scan QR to view any case</Text>
        <Image source={require('../../../../assets/images/image.png')} style={styles.qrImage} />
      </TouchableOpacity>

      <Modal visible={isModalVisible} animationType="slide">
      <QRScanner
          onScanComplete={handleScanComplete}
          onClose={handleCloseModal}
        />
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#fff",
    padding: 30,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "center",
    gap: 30,
    width: "100%",
    marginBottom: 50,
  },
  caseButton: {
    width: 140,
    height: 140,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 3,
    borderColor: "#d0d9f5",
    borderRadius: 16,
    borderStyle: "dashed",
    backgroundColor: "#fff",
  },
  buttonIcon: {
    width: 40,
    height: 40,
    resizeMode: "contain",
    tintColor: "#0546a1",
  },
  buttonText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.primary,
    textAlign: "center",
  },
  qrSection: {
    alignItems: "center",
    marginVertical: 30,
  },
  qrTitle: {
    fontSize: 22,
    fontWeight: "bold",
    color: Colors.primary,
  },
  qrSubtitle: {
    fontSize: 16,
    color: "gray",
    marginBottom: 15,
  },
  qrImage: {
    width: 180,
    height: 180,
    resizeMode: "contain",
    marginLeft: 13,
  },
  logoutContainer: {
    position: "absolute",
    bottom: 50,
    alignItems: "center",
  },
  userText: {
    fontSize: 18,
    marginBottom: 12,
    fontWeight: "bold",
    fontFamily: 'Roboto_bold',
    color: Colors.primary,
  },
});