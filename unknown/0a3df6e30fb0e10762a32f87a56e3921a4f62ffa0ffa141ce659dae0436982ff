// hooks/useCameraPermissions.js
import { useState, useEffect } from 'react';
import { Camera } from 'expo-camera';

export const useCameraPermissions = () => {
  const [permission, setPermission] = useState(null);

  const requestPermission = async () => {
    const { status } = await Camera.requestCameraPermissionsAsync();
    setPermission({
      granted: status === 'granted',
      status: status
    });
    return status === 'granted';
  };

  useEffect(() => {
    (async () => {
      const { status } = await Camera.getCameraPermissionsAsync();
      setPermission({
        granted: status === 'granted',
        status: status
      });
    })();
  }, []);

  return [permission, requestPermission];
};

export default useCameraPermissions;