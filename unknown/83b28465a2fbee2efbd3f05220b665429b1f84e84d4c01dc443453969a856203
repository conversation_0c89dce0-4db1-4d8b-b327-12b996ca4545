import React from 'react';
import { View, Image, TouchableOpacity, StyleSheet, Text } from 'react-native';
import { MaterialIcons, AntDesign } from '@expo/vector-icons';

const PDF_THUMBNAIL = require('../../assets/images/pdf-icon.png');

const PDF_FALLBACK_COLOR = '#F9FAFB';

const MediaThumbnail = ({ uri, type, name, compressed, onDelete, thumbnailSize }) => {
  const renderContent = () => {
    if (type === 'pdf') {
      return (
        <View style={styles.pdfContainer}>
          <Image 
            source={PDF_THUMBNAIL} 
            style={styles.pdfImage}
            defaultSource={PDF_THUMBNAIL}
            onError={(e) => {
              // If image fails to load, the View's background color will show
              console.log('PDF thumbnail failed to load:', e.nativeEvent.error);
            }}
          />
          <Text style={styles.pdfText} numberOfLines={1} ellipsizeMode="middle">
            {name || 'Document.pdf'}
          </Text>
        </View>
      );
    } else {
      return (
        <Image source={{ uri }} style={styles.thumbnail} resizeMode="cover" />
      );
    }
  };

  return (
    <View style={[styles.mediaItem, { width: thumbnailSize, height: thumbnailSize }]}>
      {renderContent()}
      
      {type === 'video' && (
        <MaterialIcons
          name="videocam"
          size={24}
          color="white"
          style={styles.mediaTypeIcon}
        />
      )}
      
      {compressed && (
        <MaterialIcons
          name="compress"
          size={20}
          color="white"
          style={[styles.mediaTypeIcon, { right: 5, left: undefined }]}
        />
      )}
      
      <TouchableOpacity style={styles.deleteButton} onPress={onDelete}>
        <AntDesign name="closecircle" size={20} color="red" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  mediaItem: {
    borderRadius: 8,
    overflow: 'hidden',
    margin: 5,
    position: 'relative',
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  pdfContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 5,
    backgroundColor: PDF_FALLBACK_COLOR,
  },
  pdfImage: {
    width: '70%',
    height: '70%',
    resizeMode: 'contain',
  },
  pdfText: {
    marginTop: 5,
    fontSize: 12,
    color: '#4B5563',
    textAlign: 'center',
    fontWeight: '500',
    width: '100%',
  },
  mediaTypeIcon: {
    position: 'absolute',
    bottom: 5,
    left: 5,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 12,
    padding: 2,
  },
  deleteButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: 'white',
    borderRadius: 12,
  },
});

export default MediaThumbnail;