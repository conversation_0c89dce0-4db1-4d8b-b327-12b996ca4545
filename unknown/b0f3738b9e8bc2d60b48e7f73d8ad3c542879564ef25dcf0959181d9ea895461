import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../constants/colors';

export default function DispatcherCustomHeader({
  title,
  searchHandler,
  notificationHandler,
  showSearchIcon = true,           // Control search icon visibility
  showNotificationIcon = true,      // Control notification icon visibility
  searchIcon,                      // Custom search icon
  notificationIcon,                // Custom notification icon
  searchIconColor = Colors.lightText,     // Default search icon color
  notificationIconColor = Colors.lightText, // Default notification icon color
  titleColor = '#333',             // Title text color
  backgroundColor = '#fff',        // Header background color
  borderBottomColor = Colors.borderColor,   // Border bottom color
  borderBottomWidth = 1,           // Border bottom width (0 to disable)
  headerPaddingHorizontal = 25,    // Horizontal padding
  headerPaddingVertical = 20,      // Vertical padding
  titleFontSize = 18,              // Title font size
  iconSize = 22,                   // Default icon size
}) {
  return (
    <View
      style={[
        styles.headerContainer,
        {
          backgroundColor,
          borderBottomColor,
          borderBottomWidth,
          paddingHorizontal: headerPaddingHorizontal,
          paddingVertical: headerPaddingVertical,
        },
      ]}
    >
      {/* Left Side: Title */}
      <View style={styles.leftContainer}>
        <Text
          style={[
            styles.title,
            { color: titleColor, fontSize: titleFontSize },
          ]}
        >
          {title}
        </Text>
      </View>

      {/* Right Icons (Search + Notifications) */}
      <View style={styles.rightIcons}>
        {showSearchIcon && searchHandler && (
          <TouchableOpacity onPress={searchHandler}>
            {searchIcon || (
              <Ionicons
                name="search-outline"
                size={iconSize}
                color={searchIconColor}
              />
            )}
          </TouchableOpacity>
        )}
        {showNotificationIcon && notificationHandler && (
          <TouchableOpacity onPress={notificationHandler}>
            {notificationIcon || (
              <Ionicons
                name="notifications-outline"
                size={iconSize}
                color={notificationIconColor}
              />
            )}
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontWeight: 'bold',
    fontFamily: 'Roboto_bold',
  },
  rightIcons: {
    flexDirection: 'row',
    gap: 23,
  },
});