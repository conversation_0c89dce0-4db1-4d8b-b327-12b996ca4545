# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/
/.pnp
.pnp.js

# Expo
.expo/
dist/
web-build/
expo-env.d.ts
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
web-build/


/android
/ios

android/
ios/

# iOS specific (keep essential files, ignore build artifacts)
ios/Pods/
ios/build/
ios/*.xcworkspace/xcuserdata/
ios/*.xcodeproj/xcuserdata/
ios/*.xcodeproj/project.xcworkspace/xcuserdata/
ios/DerivedData/
ios/.hmap
ios/.svn/
ios/.DS_Store
ios/Icon?
ios/Podfile.lock
ios/*.mode1v3
ios/*.mode2v3
ios/*.perspectivev3
ios/*.xccheckout
ios/*.moved-aside
ios/*.pbxuser
ios/*.perspectivev3
ios/*.xcuserstate
ios/*.xcscmblueprint

# Android specific
android/app/build/
android/app/release/
android/app/debug/
android/.gradle/
android/local.properties
android/app/debug.keystore
android/app/release.keystore
android/app/*.keystore
android/app/*.jks
android/app/*.p8
android/app/*.p12
android/app/*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local
.env
.env.development
.env.test
.env.production
.env.local
.env.preview

# typescript
*.tsbuildinfo
.expo

# IDE
.idea/
.vscode/
*.swp
*.swo

# Build
/build
/ios/build
/android/build
/android/app/build

# Coverage
/coverage

# Temporary files
*.log
*.tmp
*.temp
.cache/

# Web specific
# Build output
/dist
/build
/out
/.next
/.nuxt
/.output

# Cache directories
.cache/
.npm/
.eslintcache
.stylelintcache

# Package manager
yarn.lock
package-lock.json
pnpm-lock.yaml

# Testing
/cypress/videos/
/cypress/screenshots/
/playwright-report/
/test-results/

# Misc
*.bak
*.swp
*.swo
*.swn
*.orig
.history/
.temp/
.tmp/

# Expo specific
.expo-shared/
.expo/
dist/
web-build/

# Development builds
*.apk
*.aab
*.ipa
*.app
*.dSYM.zip
*.dSYM