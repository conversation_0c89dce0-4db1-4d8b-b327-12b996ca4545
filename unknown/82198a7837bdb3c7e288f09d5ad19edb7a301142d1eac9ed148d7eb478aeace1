import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Image, useWindowDimensions, Platform, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import * as Print from 'expo-print';
import { useRouter, useLocalSearchParams } from 'expo-router';

const DipatchQr = () => {
  const router = useRouter();
  const [isPrinting, setIsPrinting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { width, height } = useWindowDimensions();
  const { dispatchData } = useLocalSearchParams();

  const parsedDispatchData = JSON.parse(dispatchData);
  const { dispatcherQr, requestId, caseId } = parsedDispatchData;

  const isTablet = width > 768 || height > 768;
  const qrSize = isTablet 
    ? Math.min(width, height) * 0.35 
    : Math.min(width, height) * 0.5;  

  useEffect(() => {
    if (dispatcherQr) {
      setIsLoading(false);
    } else {
      setIsLoading(false);
    }
  }, [dispatcherQr]);

  if (isLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#3CC784" />
      </View>
    );
  }

  const handleNextPress = () => {
    router.dismissTo({
      pathname: '(screensDispatchers)/dispatcherRecentCases',
    //   params: {
    //     caseid: caseId, 
    //   }
    });
  };

  const handlePrint = async () => {
    if (!dispatcherQr) {
      Alert.alert('Error', 'No QR code available to print');
      return;
    }

    try {
      setIsPrinting(true);

      const htmlContent = `
        <html>
          <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
            <style>
              @page {
                size: 3in 3in; /* Standard sticky note size (approximately 76mm x 76mm) */
                margin: 0.1in; /* Small margin to ensure content isn't cut off */
              }
              body {
                margin: 0;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                background-color: white;
                width: 2.8in; /* Accounting for margins */
                height: 2.8in;
              }
              .qr-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              }
              .qr-image {
                width: 2.5in; /* Slightly smaller than container */
                height: 2.5in;
                object-fit: contain;
              }
              .request-id {
                font-size: 8pt;
                margin-top: 0.1in;
                color: #333;
              }
            </style>
          </head>
          <body>
            <div class="qr-container">
              <img src="${dispatcherQr}" class="qr-image" alt="QR Code" />
              <div class="request-id">ID: ${requestId}</div>
            </div>
          </body>
        </html>
      `;

      await Print.printAsync({
        html: htmlContent,
        orientation: 'portrait',
      });
    } catch (error) {
      console.error('Printing error:', error);
      Alert.alert('Print Error', 'There was an error while printing. Please try again.');
    } finally {
      setIsPrinting(false);
    }
  };

  const checkPrinter = async () => {
    if (Platform.OS === 'web') {
      try {
        // Check if printing is supported
        if (typeof window !== 'undefined' && 'print' in window) {
          console.log('Printing is supported');
          
          // On some browsers, you can check available printers
          if ('getPrinters' in window) {
            const printers = await window.getPrinters();
            console.log('Available printers:', printers);
          }
        }
      } catch (error) {
        console.error('Error checking printer:', error);
      }
    }
  };

  return (
    <View style={styles.container}>
      {dispatcherQr ? (
        <View style={styles.contentContainer}>
          <Text style={styles.successText}>Dispatcher QR code generated successfully!</Text>
          
          {/* QR Code with Corner Brackets */}
          <View style={styles.qrWrapper}>
            {/* Top-left corner */}
            <View style={[styles.corner, styles.topLeft]}>
              <View style={styles.cornerHorizontal} />
              <View style={styles.cornerVertical} />
            </View>
            
            {/* Top-right corner */}
            <View style={[styles.corner, styles.topRight]}>
              <View style={styles.cornerHorizontal} />
              <View style={styles.cornerVertical} />
            </View>
            
            {/* QR Code */}
            <Image
              source={{ uri: dispatcherQr }}
              style={[styles.qrImage, { width: qrSize, height: qrSize }]}
              resizeMode="contain"
            />
            
            {/* Bottom-left corner */}
            <View style={[styles.corner, styles.bottomLeft]}>
              <View style={styles.cornerHorizontal} />
              <View style={styles.cornerVertical} />
            </View>
            
            {/* Bottom-right corner */}
            <View style={[styles.corner, styles.bottomRight]}>
              <View style={styles.cornerHorizontal} />
              <View style={styles.cornerVertical} />
            </View>
          </View>
          
          <TouchableOpacity 
            style={styles.printButton}
            onPress={handlePrint}
            disabled={isPrinting}
          >
            <Text style={styles.printButtonText}>
              {isPrinting ? 'Printing...' : 'Print'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.nextButton}
            onPress={handleNextPress}
          >
            <Text style={styles.nextButtonText}>Next</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <Text style={styles.noDataText}>No QR code data available. Please ensure a QR code is provided.</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  successText: {
    fontSize: 20,
    color: '#3CC784',
    fontWeight: '500',
    marginBottom: 30,
    textAlign: 'center',
  },
  qrWrapper: {
    position: 'relative',
    padding: 20,
    marginBottom: 40,
  },
  qrImage: {
    backgroundColor: '#fff',
  },
  // Corner bracket styles
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
  },
  cornerHorizontal: {
    position: 'absolute',
    width: 30,
    height: 2,
    backgroundColor: '#0B36A1',
  },
  cornerVertical: {
    position: 'absolute',
    width: 2,
    height: 30,
    backgroundColor: '#0B36A1',
  },
  topLeft: {
    top: 0,
    left: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    transform: [{rotate: '90deg'}],
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    transform: [{rotate: '-90deg'}],
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    transform: [{rotate: '180deg'}],
  },
  printButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 60,
    paddingVertical: 10,
    paddingHorizontal: 40,
    borderWidth: 1,
    borderColor: '#0B36A1',
    marginBottom: 20,
  },
  printButtonText: {
    color: '#0B36A1',
    fontSize: 16,
    fontWeight: '500',
    marginRight: 5,
  },
  nextButton: {
    backgroundColor: '#0B36A1',
    paddingVertical: 12,
    paddingHorizontal: 40,
    borderRadius: 50,
  },
  nextButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  noDataText: {
    fontSize: 16,
    color: '#888',
    textAlign: 'center',
  },
});

export default DipatchQr;