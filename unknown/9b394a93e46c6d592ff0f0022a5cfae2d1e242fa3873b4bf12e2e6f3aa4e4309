import 'dotenv/config';
import { config } from 'dotenv';

const env = process.env.APP_ENV || 'development';
config({ path: `.env.${env}` });

const getExtraConfig = () => {
  return {
    baseUrl: process.env.API_BASE_URL,
    router: {
      origin: false
    },
    appEnv: env,
    eas: {
      projectId: "1cc647b6-80e9-4a1f-ae56-523050f88acf"
    }
  };
};

export default {
  expo: {
    name: "<PERSON>tya Samadhan Judiciary",
    slug: "satya-samadhan-judiciary",
    scheme: "satya-samadhan-judiciary",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/iphone-Icon.png",
    userInterfaceStyle: "light",
    newArchEnabled: true,
    splash: {
      image: "./assets/images/splash.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff"
    },
    ios: {
      supportsTablet: true,
      config: {
        googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY
      }
    },
    android: {
      package: "com.daakia.satyaSamadhan.judiciary.app",
      adaptiveIcon: {
        foregroundImage: "./assets/images/android-icon.png",
        backgroundColor: "#ffffff"
      },
      permissions: [
        "android.permission.CAMERA",
        "android.permission.RECORD_AUDIO",
        "android.permission.ACCESS_FINE_LOCATION",
        "android.permission.READ_EXTERNAL_STORAGE",
        "android.permission.WRITE_EXTERNAL_STORAGE",
        "android.permission.ACCESS_COARSE_LOCATION",
        "android.permission.MODIFY_AUDIO_SETTINGS"
      ],
      config: {
        googleMaps: {
          apiKey: process.env.GOOGLE_MAPS_API_KEY
        }
      }
    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/iphone-Icon.png"
    },
    extra: getExtraConfig(),
    plugins: [
      "expo-router",
      [
        "react-native-document-scanner-plugin",
        {
          "cameraPermission": "Allow Satya Samadhan Judiciary to access your camera for document scanning."
        }
      ],
      "react-native-vision-camera",
      [
        "expo-splash-screen",
        {
          image: "./assets/images/android-icon.png",
          resizeMode: "cover",
          dark: {
            image: "./assets/images/android-icon.png"
          },
          imageWidth: 200
        }
      ],
      [
        "expo-image-picker",
        {
          "photosPermission": "Allow Satya Samadhan Forensic to access your photos to enable image selection.",
          "cameraPermission": "Allow Satya Samadhan Forensic to use your camera for capturing photos."
        }
      ],
      [
        "expo-media-library",
        {
          "photosPermission": "Allow Satya Samadhan Judiciary to access your photos.",
          "savePhotosPermission": "Allow Satya Samadhan Judiciary to save photos.",
          "isAccessMediaLocationEnabled": false
        }
      ],
      [
        "expo-calendar",
        {
          calendarPermission: "Allow Satya Samadhan Judiciary to access your calendar to manage events and schedules.",
          remindersPermission: "Allow Satya Samadhan Judiciary to access your reminders to manage tasks and events."
        }
      ],
      [
        "expo-image-picker",
        {
          photosPermission: "Allow Satya Samadhan Judiciary to access your photos to enable image selection.",
          cameraPermission: "Allow Satya Samadhan Judiciary to use your camera for capturing photos."
        }
      ],
      [
        "expo-camera",
        {
          cameraPermission: "Allow Satya Samadhan Judiciary to access your camera for photo and video capture.",
          microphonePermission: "Allow Satya Samadhan Judiciary to access your microphone for recording audio during video capture.",
          recordAudioAndroid: true
        }
      ],
      [
        "expo-location",
        {
          locationAlwaysAndWhenInUsePermission: "Allow Satya Samadhan Judiciary to access your location to provide location-based services."
        }
      ],
      [
        "expo-av",
        {
          microphonePermission: "Allow Satya Samadhan Judiciary to access your microphone."
        }
      ],
      "expo-secure-store",
      "expo-video"
    ],
    runtimeVersion: {
      policy: "appVersion"
    },
    experiments: {
      typedRoutes: true
    },
    updates: {
      fallbackToCacheTimeout: 0,
      url: `https://u.expo.dev/1cc647b6-80e9-4a1f-ae56-523050f88acf`
    }
  }
}; 