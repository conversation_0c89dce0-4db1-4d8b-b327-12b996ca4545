import React from 'react';
import { View, StyleSheet } from 'react-native';
import ImagePreview from './ImagePreview';

const PreviewComponent = ({ uri, onClose }) => {
  // Safe function to check if a file is a video
  const isVideoFile = (fileUri) => {
    try {
      if (!fileUri || typeof fileUri !== 'string') return false;
      const lowerCaseUri = fileUri.toLowerCase();
      return lowerCaseUri.endsWith('.mp4') || lowerCaseUri.endsWith('.mov');
    } catch (error) {
      console.warn('Error checking file type:', error);
      return false;
    }
  };

  const isVideo = isVideoFile(uri);

  return (
    <View style={styles.container}>
      <ImagePreview 
        uri={uri || ''} // Ensure uri is never null/undefined
        onClose={onClose || (() => {})} // Ensure onClose is always a function
        isVideo={isVideo}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.85)', // Semi-transparent background
  }
});

export default PreviewComponent;