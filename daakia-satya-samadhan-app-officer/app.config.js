import 'dotenv/config';
import { config } from 'dotenv';

const env = process.env.APP_ENV || 'development';
config({ path: `.env.${env}` });

const getExtraConfig = () => {
  return {
    baseUrl: process.env.API_BASE_URL,
    router: {
      origin: false
    },
    appEnv: env,
    eas: {
      projectId: "e9f3d9e7-518d-42cc-a9a3-82c3ec465d97"
    }
  };
};

export default {
  expo: {
    name: "<PERSON><PERSON><PERSON>",
    slug: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    scheme: "satya-samadhan",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/iphone-Icon.png",
    userInterfaceStyle: "light",
    newArchEnabled: true,
    splash: {
      image: "./assets/images/splash.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff"
    },
    ios: {
      supportsTablet: true,
      config: {
        googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY
      }
    },
    android: {
      package: "com.daakia.satyaSamadhan.app",
      adaptiveIcon: {
        foregroundImage: "./assets/images/android-icon.png",
        backgroundColor: "#ffffff"
      },
      permissions: [
        "android.permission.CAMERA",
        "android.permission.RECORD_AUDIO",
        "android.permission.ACCESS_FINE_LOCATION",
        "android.permission.READ_EXTERNAL_STORAGE",
        "android.permission.WRITE_EXTERNAL_STORAGE",
        "android.permission.ACCESS_COARSE_LOCATION",
        "android.permission.MODIFY_AUDIO_SETTINGS"
      ],
      config: {
        googleMaps: {
          apiKey: process.env.GOOGLE_MAPS_API_KEY
        }
      }
    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/iphone-Icon.png"
    },
    extra: getExtraConfig(),
    plugins: [
      "expo-router",
      [
        "expo-splash-screen",
        {
          image: "./assets/images/android-icon.png",
          resizeMode: "cover",
          dark: {
            image: "./assets/images/android-icon.png"
          },
          imageWidth: 200
        }
      ],
      [
        "expo-image-picker",
        {
          photosPermission: "Allow Satya Samadhan to access your photos to enable image selection.",
          cameraPermission: "Allow Satya Samadhan to use your camera for capturing photos."
        }
      ],
      [
        "expo-camera",
        {
          cameraPermission: "Allow Satya Samadhan to access your camera for photo and video capture.",
          microphonePermission: "Allow Satya Samadhan to access your microphone for recording audio during video capture.",
          recordAudioAndroid: true
        }
      ],
      [
        "expo-location",
        {
          locationAlwaysAndWhenInUsePermission: "Allow Satya Samadhan to access your location to provide location-based services."
        }
      ],
      [
        "expo-av",
        {
          microphonePermission: "Allow Satya Samadhan  to access your microphone."
        }
      ],
      "expo-secure-store",
      "expo-video"
    ],
    runtimeVersion: {
      policy: "appVersion"
    },
    experiments: {
      typedRoutes: true
    },
    updates: {
      fallbackToCacheTimeout: 0,
      url: `https://u.expo.dev/e9f3d9e7-518d-42cc-a9a3-82c3ec465d97`
    }
  }
}; 