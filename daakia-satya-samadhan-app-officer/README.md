# Sa<PERSON>a <PERSON> Officer App

This is the officer application for the Satya <PERSON> platform designed for law enforcement officers.

## Technologies

- React Native with Expo
- Expo Router for navigation
- Secure authentication with JWT
- Maps and location tracking
- Camera functionality
- Local authentication
- File sharing and printing

## Prerequisites

- Node.js 16+
- npm or yarn
- Expo CLI
- iOS/Android development environment

## Installation

1. Clone the repository
```bash
git clone [repository-url]
cd daakia-satya-samadhan-app-officer
```

2. Install dependencies
```bash
npm install
```

3. Set up environment variables
Create appropriate `.env` files for different environments:
- `.env.development`
- `.env.preview`
- `.env.production`

## Running the App

```bash
# Start the development server
npx expo start

# Run on Android
npx expo start --android

# Run on iOS
npx expo start --ios

# Run on web
npx expo start --web
```

## Key Features

- Secure authentication and authorization
- Evidence collection with camera integration
- Location tracking and mapping
- Document sharing and printing
- Local biometric authentication

## Building for Production

This app uses EAS (Expo Application Services) for building. Refer to the `eas.json` file for build profiles.

```bash
# Build for development
eas build --profile development

# Build for preview
eas build --profile preview

# Build for production
eas build --profile production
```

## Project Structure

```
daakia-satya-samadhan-app-officer/
├── app/                  # Main application screens using Expo Router
├── assets/               # Images, fonts and other static assets
├── components/           # Reusable UI components
├── constants/            # Configuration constants and theme settings
├── context/              # React Context for state management
├── hooks/                # Custom React hooks
├── services/             # API services and backend integration
└── utils/                # Utility functions
```

## Dependencies

The app uses numerous Expo and React Native libraries including:
- Authentication: JWT, AsyncStorage, SecureStore
- UI: Vector Icons, Lottie animations, Blur effects
- Media: Camera, Image Picker, AV
- Location: Maps, Location services
- Navigation: Expo Router, React Navigation
- Sharing: Sharing, Printing

For a complete list of dependencies, refer to the `package.json` file. 