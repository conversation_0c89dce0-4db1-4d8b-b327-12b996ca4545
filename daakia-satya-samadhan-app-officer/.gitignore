# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/
/.pnp
.pnp.js
yarn.lock
package-lock.json

# Expo
.expo/
dist/
web-build/
expo-env.d.ts
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
*.apk
*.aab
*.ipa

# Native
android/
ios/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*
.metro/

# debug
npm-debug.*
yarn-debug.*
yarn-error.*
*.log

# macOS
.DS_Store
*.pem
.AppleDouble
.LSOverride
._*

# local env files
.env
.env*.local
.env.development
.env.test
.env.production
.env.preview

# typescript
*.tsbuildinfo

# IDE
.idea/
.vscode/
*.swp
*.swo
.project
.classpath
.settings/

# Testing
coverage/
.nyc_output/

# Temporary files
*.tmp
*.temp
.temp/
.tmp/

# Build files
build/
dist/
out/

# Cache
.cache/
.eslintcache
.stylelintcache

# System Files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# Expo
.expo/
web-build/
dist/

