import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'expo-router';
import { apiService } from '../services/api';
import { storageService } from '../services/storage';
import { validationService } from '../services/validation';
import { STORAGE_KEYS, TOKEN_REFRESH_INTERVAL, AUTH_ERRORS } from '../constants/auth';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedRole, setSelectedRole] = useState(null);
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await storageService.clearAuth();
      setUser(null);
      setProfile(null);
      setSelectedRole(null);
      router.replace('(auth)/welcome');
    } catch (error) {
      console.error('Error during logout:', error);
      setUser(null);
      setProfile(null);
      setSelectedRole(null);
      router.replace('(auth)/welcome');
    }
  };

  const refreshToken = async () => {
    try {
      const currentToken = await storageService.getItem(STORAGE_KEYS.TOKEN);
      const userId = await storageService.getItem(STORAGE_KEYS.USER_ID);
      
      if (!currentToken || !userId) {
        await handleLogout();
        return null;
      }

      const response = await apiService.refreshToken(userId, currentToken);
      
      if (response.status === 'success' && response.data.token) {
        await storageService.setItem(STORAGE_KEYS.TOKEN, response.data.token);
        setUser(prev => ({ ...prev, token: response.data.token }));
        return response.data.token;
      }
      
      await handleLogout();
      return null;
    } catch (error) {
      console.error('Failed to refresh token:', error);
      await handleLogout();
      return null;
    }
  };

  // Load auth state from storage on app start
  useEffect(() => {
    const loadAuthState = async () => {
      try {
        const token = await storageService.getItem(STORAGE_KEYS.TOKEN);
        const roles = await storageService.getItem(STORAGE_KEYS.ROLES);
        const category = await storageService.getItem(STORAGE_KEYS.CATEGORY);
        const userId = await storageService.getItem(STORAGE_KEYS.USER_ID);
        const requestId = await storageService.getItem(STORAGE_KEYS.REQUEST_ID);
        const storedSelectedRole = await storageService.getItem(STORAGE_KEYS.SELECTED_ROLE);

        if (token) {
          setUser({
            token,
            roles,
            category,
            userId,
            requestId,
          });
          setSelectedRole(storedSelectedRole);

          // Fetch profile
          const profileResponse = await apiService.fetchProfile(token);
          if (profileResponse.status === 'success') {
            setProfile(profileResponse.data);
          } else {
            console.error(AUTH_ERRORS.PROFILE_FETCH_FAILED);
            await handleLogout();
          }
        }
      } catch (error) {
        console.error('Error loading auth state:', error);
        await handleLogout();
      } finally {
        setIsLoading(false);
      }
    };

    loadAuthState();
  }, []);

  // Setup token refresh on a regular schedule
  useEffect(() => {
    const tokenRefreshInterval = setInterval(async () => {
      const token = await storageService.getItem(STORAGE_KEYS.TOKEN);
      if (token) {
        try {
          const result = await refreshToken();
          if (result) {
            console.log('Token refreshed on schedule');
          } else {
            console.log('Scheduled token refresh failed - user logged out');
          }
        } catch (error) {
          console.error('Scheduled token refresh failed:', error);
        }
      }
    }, TOKEN_REFRESH_INTERVAL);

    return () => clearInterval(tokenRefreshInterval);
  }, []);

  const login = async (mobileNumber) => {
    validationService.validateMobile(mobileNumber);
    setIsLoading(true);
    
    try {
      const response = await apiService.login(mobileNumber);
      
      if (response.status === 'success') {
        return {
          userId: response.data.userId,
          requestId: response.data.requestId,
        };
      } else {
        throw new Error(response.message || 'Login failed.');
      }
    } catch (error) {
      throw new Error(
        error.response?.data?.message ||
        'An unexpected error occurred. Please try again later.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOtp = async (userId, requestId, otp) => {
    validationService.validateOtp(otp);

    try {
      const response = await apiService.verifyOtp(userId, requestId, otp);

      if (response.status === 'success') {
        const roles = response.data.role;
        const defaultRole = roles.includes('officer') ? 'officer' : roles[0];
        const token = response.data.token;

        // Store auth data
        await storageService.setItem(STORAGE_KEYS.TOKEN, token);
        await storageService.setItem(STORAGE_KEYS.ROLES, roles);
        await storageService.setItem(STORAGE_KEYS.SELECTED_ROLE, defaultRole);
        await storageService.setItem(STORAGE_KEYS.CATEGORY, response.data.category);
        await storageService.setItem(STORAGE_KEYS.USER_ID, userId);
        await storageService.setItem(STORAGE_KEYS.REQUEST_ID, requestId);

        // Update state
        setUser({
          token,
          roles,
          category: response.data.category,
          userId,
          requestId,
        });
        setSelectedRole(defaultRole);

        // Fetch profile
        const profileResponse = await apiService.fetchProfile(token);
        if (profileResponse.status === 'success') {
          setProfile(profileResponse.data);
        }

        return true;
      } else {
        throw new Error(response.message || 'OTP verification failed');
      }
    } catch (error) {
      throw new Error(
        error.response?.data?.message ||
        'An error occurred while verifying OTP'
      );
    }
  };

  const updateSelectedRole = async (newRole) => {
    if (!user?.roles.includes(newRole)) {
      throw new Error('Invalid role selection.');
    }
    await storageService.setItem(STORAGE_KEYS.SELECTED_ROLE, newRole);
    setSelectedRole(newRole);
  };

  const updateUserProfile = async (updateData) => {
    try {
      const validatedData = validationService.validateProfileUpdate(updateData);
      const response = await apiService.updateProfile(user.token, validatedData);
      
      if (response.status === 'success') {
        setProfile(response.data);
        return true;
      } else {
        throw new Error(response.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating user profile:', error.response?.data?.message || error.message);
      throw error;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        profile,
        isLoading,
        login,
        verifyOtp,
        logout: handleLogout,
        selectedRole,
        updateSelectedRole,
        userId: user?.userId || null,
        requestId: user?.requestId || null,
        token: user?.token || null,
        refreshToken,
        updateUserProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);