import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
  Keyboard,
} from 'react-native';
import { router } from 'expo-router';
import { useAuth } from '../../context/auth-context';
import { StatusBar } from 'expo-status-bar';
import { AntDesign } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');
const isTablet = width >= 768;
const isWindows = width > 1068;

export default function Login() {
  const [mobileNumber, setMobileNumber] = useState('');
  const { login, isLoading } = useAuth();
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  // Add keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const handleLogin = async () => {
    // Your login handling code remains the same
    if (!mobileNumber || mobileNumber.length !== 10) {
      Alert.alert('Error', 'Mobile number must be exactly 10 digits.');
      return;
    }

    try {
      const loginData = await login(mobileNumber);
      if (!loginData || !loginData.userId || !loginData.requestId) {
        throw new Error('Invalid response from server');
      }
      
      router.replace({
        pathname: '/(auth)/verify-otp',
        params: {
          userId: loginData.userId,
          requestId: loginData.requestId,
        },
      });
    } catch (error) {
      Alert.alert('Error', error.message || 'Failed to login. Please try again.');
    }
  };


  const handleBackNavigation = () => {

    router.replace('/(auth)/welcome');
  };

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />
      
      {/* Updated Back Button with < icon and specific route navigation */}
      <TouchableOpacity 
        style={styles.backButton}
        onPress={handleBackNavigation}
      >
        <AntDesign name="left" size={22} color="#0B36A1" />
      </TouchableOpacity>

      {/* Logo section - outside KeyboardAvoidingView */}
      <View style={[
        styles.logoContainer,
        keyboardVisible && Platform.OS === 'ios' && { height: height * 0.15 }
      ]}>
        <Image 
          source={require('../../assets/images/satya_samadhan_logo.png')} 
          style={[
            styles.logo,
            keyboardVisible && { height: isTablet ? 200 : 100, width: isTablet ? 175 : 100 }
          ]} 
        />
        <Text style={styles.slogan}>
          "Synergizing Law Enforcement With Justice Delivery System"
        </Text>
      </View>

      {/* Login section - with KeyboardAvoidingView */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.loginBoxContainer}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <View style={styles.loginBox}>
          <Text style={styles.loginTitle}>Login</Text>
          <Text style={styles.loginSubtitle}>Please login to continue</Text>

          <View style={styles.inputContainer}>
            <Image source={require('../../assets/images/india.png')} style={styles.flagIcon} />
            <TextInput
              placeholder="Mobile Number*"
              keyboardType="phone-pad"
              style={[styles.input, { paddingLeft: 2 }]}
              placeholderTextColor="#fff"
              maxLength={10}
              value={mobileNumber}
              onChangeText={(text) => setMobileNumber(text.replace(/[^0-9]/g, '').slice(0, 10))}
            />
          </View>

          <View style={styles.loginContainer}>
            <TouchableOpacity
              style={[
                styles.loginButton,
                (!mobileNumber || mobileNumber.length !== 10) && styles.disabledButton,
              ]}
              onPress={handleLogin}
              disabled={isLoading || !mobileNumber || mobileNumber.length !== 10}
            >
              {isLoading ? (
                <ActivityIndicator color="#0B36A1" />
              ) : (
                <Text style={styles.loginButtonText}>LOGIN</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  logoContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    fontFamily: 'Roboto',
  },
  logo: {
    width: isWindows ? 200 : isTablet ? 350 : 200,
    height: isWindows ? 200 : isTablet ? 400 : 200,
    resizeMode: 'contain',
    fontFamily: 'Roboto',
  },
  slogan: {
    fontSize: isTablet ? 18 : 14,
    color: '#0B36A1',
    marginHorizontal: 70,
    textAlign: 'center',
    fontFamily: 'Roboto',
  },
  loginBoxContainer: {
    width: '100%',
  },
  loginBox: {
    alignSelf: 'stretch',
    backgroundColor: '#0B36A1',
    paddingBottom: isWindows ? 50 : isTablet ? 70 : 60,
    paddingTop: isWindows ? 50 : isTablet ? 70 : 60,
    paddingHorizontal: isTablet ? 70 : 26,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    fontFamily: 'Roboto',
  },
  // Rest of your styles remain the same
  loginTitle: {
    fontSize: isTablet ? 52 : 36,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
    fontFamily: 'Roboto',
  },
  loginSubtitle: {
    fontSize: isTablet ? 18 : 14,
    color: '#fff',
    marginBottom: 30,
    fontFamily: 'Roboto',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#fff',
    borderRadius: 10,
    paddingHorizontal: 15,
    fontFamily: 'Roboto',
  },
  input: {
    flex: 1,
    height: 50,
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  flagIcon: {
    width: 30,
    height: 20,
    marginRight: 10,
    resizeMode: 'contain',
  },
  loginButton: {
    backgroundColor: '#fff',
    paddingVertical: 15,
    paddingHorizontal: 100,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 10,
    fontFamily: 'Roboto',
  },
  disabledButton: {
    backgroundColor: '#c4c4c4',
  },
  loginContainer: {
    paddingHorizontal: isTablet ? 100 : 0,
  },
  loginButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0B36A1',
    fontFamily: 'Roboto_Bold',
  },
  backButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 50,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    elevation: 1000,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2.5,
    borderWidth: 1,
    borderColor: 'rgba(11, 54, 161, 0.1)',
  },
});