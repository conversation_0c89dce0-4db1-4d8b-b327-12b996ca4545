import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Image, useWindowDimensions, Platform, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import * as Print from 'expo-print';
import { useRouter, useLocalSearchParams } from 'expo-router';

const QRPageMaster = () => {
  const router = useRouter();
  const [isPrinting, setIsPrinting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { width, height } = useWindowDimensions();
  const { MasterQr, caseid } = useLocalSearchParams(); 

  const isTablet = width > 768 || height > 768;
  const qrSize = isTablet 
    ? Math.min(width, height) * 0.35 // Smaller relative size on tablets
    : Math.min(width, height) * 0.5;  // Larger relative size on phones

  useEffect(() => {
    // Check if we have the necessary data from params
    if (MasterQr) {
      setIsLoading(false);
    } else {
      // If no MasterQr in params, we can't proceed
      setIsLoading(false);
    }
  }, [MasterQr]);

  if (isLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#3CC784" />
      </View>
    );
  }

  const handleNextPress = () => {
    router.dismissTo({
      pathname: '(screens)/caseDetails',
      params: {
        caseid: caseid, 
      },
    
    });
  };

  const handleAddMoreEvidence = () => {
    router.replace({
      pathname: '(screens)/capturePackage',
      params: {
        caseid: caseid, 
      }
    });
  };

  const handlePrint = async () => {
    if (!MasterQr) {
      Alert.alert('Error', 'No QR code available to print');
      return;
    }

    try {
      setIsPrinting(true);

      const htmlContent = `
        <html>
          <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
            <style>
              @page {
                size: auto;
                margin: 0mm;
              }
              html, body {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
                overflow: hidden;
              }
              body {
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: white;
              }
              .qr-wrapper {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
              }
              .qr-image {
                width: 95%;
                height: 95%;
                object-fit: contain;
              }
            </style>
          </head>
          <body>
            <div class="qr-wrapper">
              <img src="${MasterQr}" class="qr-image" alt="QR Code" />
            </div>
          </body>
        </html>
      `;

      if (Platform.OS === 'web') {
        // For web, open in a new window optimized for printing
        const printWindow = window.open('', '_blank');
        
        if (printWindow) {
          printWindow.document.write(htmlContent);
          printWindow.document.close();
          
          // Ensure image is loaded before printing
          setTimeout(() => {
            printWindow.focus();
            printWindow.print();
            printWindow.close();
          }, 500);
        } else {
          Alert.alert('Print Error', 'Unable to open print window. Please check if pop-ups are blocked.');
        }
      } else {
        // Use Expo Print for native platforms
        await Print.printAsync({
          html: htmlContent,
          orientation: 'portrait',
        });
      }
    } catch (error) {
      console.error('Printing error:', error);
      Alert.alert('Print Error', 'There was an error while printing. Please try again.');
    } finally {
      setIsPrinting(false);
    }
  };

  return (
    <View style={styles.container}>
      {MasterQr ? (
        <View style={styles.contentContainer}>
          <Text style={styles.successText}>Case QR code generated successfully!</Text>
          
          {/* QR Code with Corner Brackets */}
          <View style={styles.qrWrapper}>
            {/* Top-left corner */}
            <View style={[styles.corner, styles.topLeft]}>
              <View style={styles.cornerHorizontal} />
              <View style={styles.cornerVertical} />
            </View>
            
            {/* Top-right corner */}
            <View style={[styles.corner, styles.topRight]}>
              <View style={styles.cornerHorizontal} />
              <View style={styles.cornerVertical} />
            </View>
            
            {/* QR Code */}
            <Image
              source={{ uri: MasterQr }}
              style={[styles.qrImage, { width: qrSize, height: qrSize }]}
              resizeMode="contain"
            />
            
            {/* Bottom-left corner */}
            <View style={[styles.corner, styles.bottomLeft]}>
              <View style={styles.cornerHorizontal} />
              <View style={styles.cornerVertical} />
            </View>
            
            {/* Bottom-right corner */}
            <View style={[styles.corner, styles.bottomRight]}>
              <View style={styles.cornerHorizontal} />
              <View style={styles.cornerVertical} />
            </View>
          </View>
          
          <TouchableOpacity 
            style={styles.printButton}
            onPress={handlePrint}
            disabled={isPrinting}
          >
            <Text style={styles.printButtonText}>
              {isPrinting ? 'Printing...' : 'Print'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.nextButton}
            onPress={handleNextPress}
          >
            <Text style={styles.nextButtonText}>Next</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <Text style={styles.noDataText}>No QR code data available. Please ensure a QR code is provided.</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  successText: {
    fontSize: 20,
    color: '#3CC784',
    fontWeight: '500',
    marginBottom: 30,
    textAlign: 'center',
  },
  qrWrapper: {
    position: 'relative',
    padding: 20,
    marginBottom: 40,
  },
  qrImage: {
    backgroundColor: '#fff',
  },
  // Corner bracket styles
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
  },
  cornerHorizontal: {
    position: 'absolute',
    width: 30,
    height: 2,
    backgroundColor: '#0B36A1',
  },
  cornerVertical: {
    position: 'absolute',
    width: 2,
    height: 30,
    backgroundColor: '#0B36A1',
  },
  topLeft: {
    top: 0,
    left: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    transform: [{rotate: '90deg'}],
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    transform: [{rotate: '-90deg'}],
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    transform: [{rotate: '180deg'}],
  },
  printButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 60,
    paddingVertical: 10,
    paddingHorizontal: 40,
    borderWidth: 1,
    borderColor: '#0B36A1',
    marginBottom: 20,
  },
  printButtonText: {
    color: '#0B36A1',
    fontSize: 16,
    fontWeight: '500',
    marginRight: 5,
  },
  addMoreButton: {
    backgroundColor: '#3CC784',
    paddingVertical: 12,
    paddingHorizontal: 40,
    borderRadius: 50,
    marginBottom: 20,
  },
  addMoreButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  nextButton: {
    backgroundColor: '#0B36A1',
    paddingVertical: 12,
    paddingHorizontal: 40,
    borderRadius: 50,
  },
  nextButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  noDataText: {
    fontSize: 16,
    color: '#888',
    textAlign: 'center',
  },
});

export default QRPageMaster;