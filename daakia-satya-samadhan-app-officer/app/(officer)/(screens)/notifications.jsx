import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import React from 'react';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../../constants/colors';

// Dummy notifications data
const dummyNotifications = [
  {
    id: 1,
    title: 'New Case Assigned',
    message: 'You have been assigned to a new case #12345',
    time: '2 hours ago',
    read: false,
    icon: 'document-text-outline',
  },
  {
    id: 2,
    title: 'Evidence Update',
    message: 'New evidence has been added to case #12345',
    time: '5 hours ago',
    read: true,
    icon: 'cube-outline',
  },
  {
    id: 3,
    title: 'System Update',
    message: 'New features have been added to the app',
    time: '1 day ago',
    read: true,
    icon: 'information-circle-outline',
  },
  {
    id: 4,
    title: 'Case Status Change',
    message: 'Case #12346 status has been updated to "In Progress"',
    time: '2 days ago',
    read: true,
    icon: 'time-outline',
  },
];

export default function Notifications() {
  return (
    <View style={styles.container}>
      <View style={styles.disclaimerContainer}>
        <Ionicons name="information-circle-outline" size={20} color={Colors.primary} />
        <Text style={styles.disclaimerText}>
          These are dummy notifications. Real notification features will be implemented soon.
        </Text>
      </View>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {dummyNotifications.map((notification) => (
          <TouchableOpacity
            key={notification.id}
            style={[
              styles.notificationItem,
              !notification.read && styles.unreadNotification,
            ]}
          >
            <View style={styles.iconContainer}>
              <Ionicons
                name={notification.icon}
                size={24}
                color={Colors.primary}
              />
            </View>
            <View style={styles.contentContainer}>
              <Text style={styles.title}>{notification.title}</Text>
              <Text style={styles.message}>{notification.message}</Text>
              <Text style={styles.time}>{notification.time}</Text>
            </View>
            {!notification.read && <View style={styles.unreadDot} />}
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  disclaimerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${Colors.primary}10`,
    padding: 12,
    margin: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  disclaimerText: {
    fontSize: 14,
    fontFamily: 'Roboto',
    color: Colors.primary,
    marginLeft: 8,
    flex: 1,
  },
  scrollContainer: {
    padding: 16,
  },
  notificationItem: {
    flexDirection: 'row',
    backgroundColor: Colors.background,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.border,
    alignItems: 'center',
  },
  unreadNotification: {
    backgroundColor: `${Colors.primary}10`,
    borderColor: Colors.primary,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: `${Colors.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontFamily: 'Roboto_bold',
    color: Colors.black,
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    fontFamily: 'Roboto',
    color: Colors.lightText,
    marginBottom: 4,
  },
  time: {
    fontSize: 12,
    fontFamily: 'Roboto',
    color: Colors.lightText,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.primary,
    marginLeft: 8,
  },
});