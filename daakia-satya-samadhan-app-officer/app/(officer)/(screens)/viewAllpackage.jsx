import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Image, 
  TouchableOpacity, 
  ScrollView,
  Dimensions,
  StatusBar,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { AntDesign, Ionicons } from '@expo/vector-icons';
import PreviewComponent from '../../../components/Larges/PreviewComponent';
import SuccessScreen from '../../../components/Smalls/SuccessScreen';
import { transformUrl } from '../../../utils/transformUrl';

const { width } = Dimensions.get('window');
const THUMBNAIL_SIZE = width / 2 - 30; // For 2 photos per row with margins

const ViewAllEvidences = () => {
  const { caseid, MasterQr, packageImageUrls } = useLocalSearchParams();
  const [packageImages, setPackageImages] = useState([]);
  const [selectedMedia, setSelectedMedia] = useState(null);
  const [showSuccessScreen, setShowSuccessScreen] = useState(false); // State for SuccessScreen visibility
  const [loadingImages, setLoadingImages] = useState({}); // Track loading state for each image

useEffect(() => {
  // Transform the URLs passed via params
  if (packageImageUrls) {
    const transformedUrls = packageImageUrls
      .split(',')
      .map(url => transformUrl(url));
    setPackageImages(transformedUrls);
  }
}, [packageImageUrls]);

  const handleGoBack = () => {
    router.back();
  };

  const handleAddNew = () => {
    router.navigate({
      pathname: '(screens)/capturePackage',
      params: { caseid },
    });
  };

  const handleSubmit = () => {
    // Show the SuccessScreen
    setShowSuccessScreen(true);

  
    setTimeout(() => {
      setShowSuccessScreen(false);
      router.replace({
        pathname: '(screens)/qrPageMaster', 
        params: { caseid, MasterQr },
      });
    }, 3000);
  };

  const handleMediaPress = (mediaUrl) => {
    setSelectedMedia(mediaUrl);
  };

  // Function to render each evidence item
  const renderEvidenceItem = (item, index) => {
    const isVideo = item.toLowerCase().endsWith('.mp4') || item.toLowerCase().endsWith('.mov');
    const isLoading = loadingImages[item] !== false;

    return (
      <TouchableOpacity 
        key={`evidence-${index}`}
        style={styles.evidenceItem}
        onPress={() => handleMediaPress(item)} 
      >
        {isVideo ? (
          <View style={styles.videoPlaceholder}>
            <Ionicons name="videocam" size={36} color="#0B36A1" />
            <Text style={styles.videoText}>Video Evidence</Text>
          </View>
        ) : (
          <View style={styles.imageContainer}>
            {isLoading && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#0B36A1" />
              </View>
            )}
            <Image 
              source={{ uri: item }} 
              style={styles.evidenceImage}
              resizeMode="cover"
              onLoadStart={() => setLoadingImages(prev => ({ ...prev, [item]: true }))}
              onLoadEnd={() => setLoadingImages(prev => ({ ...prev, [item]: false }))}
            />
          </View>
        )}
        <View style={styles.evidenceLabelContainer}>
          <Text style={styles.evidenceLabel}>
            {`Package  ${index + 1}`}
          </Text>
          <Text style={styles.evidenceFormat}>
            {isVideo ? 'Video' : 'Image'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  // Group the images into pairs for 2-column layout
  const renderEvidenceGrid = () => {
    const rows = [];
    for (let i = 0; i < packageImages.length; i += 2) {
      const row = (
        <View key={`row-${i}`} style={styles.row}>
          {renderEvidenceItem(packageImages[i], i)}
          {i + 1 < packageImages.length && renderEvidenceItem(packageImages[i + 1], i + 1)}
        </View>
      );
      rows.push(row);
    }

    // Add the "Add New" button as the last item or in a new row
    const addNewButton = (
      <TouchableOpacity 
        style={styles.addNewButton}
        onPress={handleAddNew}
      >
        <View style={styles.addButtonIcon}>
          <AntDesign name="plus" size={24} color="#fff" />
        </View>
        <Text style={styles.addNewText}>Add New Evidence</Text>
      </TouchableOpacity>
    );

    // If the last row has only one item, add the button to that row
    // Otherwise, create a new row for the button
    if (packageImages.length % 2 === 1) {
      rows[rows.length - 1] = (
        <View key={`row-${rows.length - 1}`} style={styles.row}>
          {renderEvidenceItem(packageImages[packageImages.length - 1], packageImages.length - 1)}
          {addNewButton}
        </View>
      );
    } else {
      rows.push(
        <View key={`row-${rows.length}`} style={styles.row}>
          {addNewButton}
          <View style={styles.emptySpace} />
        </View>
      );
    }

    return rows;
  };

  return (
    <SafeAreaView style={styles.safeAreaContainer}>
      <StatusBar style='dark' />
      
      <View style={styles.container}>
        {packageImages.length > 0 ? (
          <ScrollView 
            contentContainerStyle={styles.scrollContent}
            style={styles.scrollView}
          >
            <Text style={styles.sectionTitle}>
              Packages Collection ({packageImages.length})
            </Text>
            {renderEvidenceGrid()}
          </ScrollView>
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="images-outline" size={60} color="#0B36A1" />
            <Text style={styles.emptyText}>No evidence added yet</Text>
            <TouchableOpacity 
              style={styles.addFirstButton}
              onPress={handleAddNew}
            >
              <Text style={styles.addFirstButtonText}>Add First Evidence</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Submit Button at the bottom */}
        {packageImages.length > 0 && (
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.submitButton}
              onPress={handleSubmit}
            >
              <Text style={styles.submitButtonText}>SUBMIT EVIDENCE</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Render PreviewComponent for selected media */}
        {selectedMedia && (
          <View style={styles.previewContainer}>
            <PreviewComponent 
              uri={selectedMedia} 
              onClose={() => setSelectedMedia(null)} 
            />
          </View>
        )}

        {/* Render SuccessScreen conditionally */}
        {showSuccessScreen && (
          <SuccessScreen 
            message="Package data submitted successfully!"
            duration={3000}
            onComplete={() => setShowSuccessScreen(true)}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#0B36A1', // Top background color for status bar
  },
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#0B36A1',
    padding: 16,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerRight: {
    width: 24,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100, // Extra padding for submit button
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  evidenceItem: {
    width: THUMBNAIL_SIZE,
    height: THUMBNAIL_SIZE + 50,
    borderRadius: 10,
    backgroundColor: '#fff',
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  evidenceImage: {
    width: '100%',
    height: THUMBNAIL_SIZE,
  },
  evidenceLabelContainer: {
    padding: 8,
    backgroundColor: '#fff',
  },
  evidenceLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  evidenceFormat: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  videoPlaceholder: {
    width: '100%',
    height: THUMBNAIL_SIZE,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f7ff',
  },
  videoText: {
    fontSize: 14,
    color: '#0B36A1',
    marginTop: 8,
  },
  addNewButton: {
    width: THUMBNAIL_SIZE,
    height: THUMBNAIL_SIZE,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#0B36A1',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(10, 52, 161, 0.05)',
  },
  addButtonIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#0B36A1',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  addNewText: {
    color: '#0B36A1',
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    paddingHorizontal: 10,
  },
  emptySpace: {
    width: THUMBNAIL_SIZE,
  },
  buttonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#fff',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  submitButton: {
    backgroundColor: '#0B36A1',
    padding: 16,
    borderRadius: 50,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  previewContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2000, 
    elevation: 10, 
    backgroundColor: 'rgba(0,0,0,0.9)',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    marginTop: 16,
    marginBottom: 20,
  },
  addFirstButton: {
    backgroundColor: '#0B36A1',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 10,
  },
  addFirstButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  imageContainer: {
    width: '100%',
    height: THUMBNAIL_SIZE,
    position: 'relative',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
});

export default ViewAllEvidences;