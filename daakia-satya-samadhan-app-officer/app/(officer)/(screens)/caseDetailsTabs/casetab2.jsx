import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, FlatList, Image, StyleSheet, TouchableOpacity, ActivityIndicator, Alert, Platform, Modal } from 'react-native';
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { useAuth } from "../../../../context/auth-context";
import { transformUrl } from "../../../../utils/transformUrl";
import * as FileSystem from 'expo-file-system';
import * as IntentLauncher from 'expo-intent-launcher';
import * as Sharing from 'expo-sharing';
import * as WebBrowser from 'expo-web-browser';
import PreviewComponent from '../../../../components/Larges/PreviewComponent';
import { Colors } from '../../../../constants/colors';
import { apiService } from "../../../../services/api";
import { ForensicReportAccessEnum } from "../../../../constants/auth";



const Tab2 = ({ caseid, changeTab }) => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [downloadingId, setDownloadingId] = useState(null);
  const [previewUri, setPreviewUri] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const { token, userId } = useAuth();
  
  // Track permission statuses for each report
  const [reportPermissions, setReportPermissions] = useState({});

  const fetchForensicReports = useCallback(async () => {
    if (!caseid) {
      setError('Case ID is missing');
      setLoading(false);
      return;
    }

    try {
      const result = await apiService.fetchForensicReports(token, caseid);


      if (result && result.status === 'success' && result.data) {
        setReports(result.data);
        // Initialize all reports with default status
        const initialPermissions = {};

        // Check access status for each report
        for (const report of result.data) {
          try {
            const accessStatus = await apiService.checkReportAccessStatus(token, report._id);
            // console.log(`Access Status for Report ${report._id}:`, accessStatus);

            // Handle different access status cases
            if (accessStatus?.status === 'success') {
              if (accessStatus?.data?.length > 0) {
                // If there's access data, use the most recent access status
                const latestAccess = accessStatus.data[accessStatus.data.length - 1];
                const accessValue = latestAccess.access;

                // Validate against enum values
                if (Object.values(ForensicReportAccessEnum).includes(accessValue)) {
                  initialPermissions[report._id] = accessValue;
                } else {
                  // console.warn(`Invalid access status received: ${accessValue}`);
                  initialPermissions[report._id] = ForensicReportAccessEnum.NO_REQUEST;
                }
              } else {
                // If data array is empty, it means no access request has been made yet
                // console.log(`No access request found for report ${report._id} - showing request option`);
                initialPermissions[report._id] = ForensicReportAccessEnum.NO_REQUEST;
              }
            } else {
              // If API call failed, default to no request state
              initialPermissions[report._id] = ForensicReportAccessEnum.NO_REQUEST;
            }
          } catch (error) {
            // console.error(`Error checking access status for report ${report._id}:`, error);
            initialPermissions[report._id] = ForensicReportAccessEnum.NO_REQUEST;
          }
        }

        setReportPermissions(initialPermissions);
      } else {
        setReports([]);
        // console.warn('Invalid response structure:', result);
      }
      setLoading(false);
    } catch (err) {
      // console.error('Error fetching forensic reports:', err);
      setError('Failed to fetch forensic reports');
      setLoading(false);
    }
  }, [caseid, token]);

  useEffect(() => {
    fetchForensicReports();
  }, [fetchForensicReports]);

  const downloadAndOpenPdf = async (url, title) => {
    if (!url) {
      Alert.alert('Error', 'No file URL provided');
      return;
    }
    
    try {
      setDownloadingId(`${title}-${url}`); // Unique ID for each URL
      const transformedUrl = transformUrl(url);
      const filename = url.split('/').pop();
      const localUri = `${FileSystem.documentDirectory}${filename}`;
      
      const downloadResumable = FileSystem.createDownloadResumable(
        transformedUrl,
        localUri,
        {},
        (downloadProgress) => {
          const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
          // console.log(`Download progress: ${progress * 100}%`);
        }
      );
      
      const { uri } = await downloadResumable.downloadAsync();
      
      if (Platform.OS === 'ios') {
        const canShare = await Sharing.isAvailableAsync();
        if (canShare) {
          await Sharing.shareAsync(uri);
        } else {
          await WebBrowser.openBrowserAsync(transformedUrl);
        }
      } else if (Platform.OS === 'android') {
        const contentUri = await FileSystem.getContentUriAsync(uri);
        await IntentLauncher.startActivityAsync('android.intent.action.VIEW', {
          data: contentUri,
          flags: 1,
          type: 'application/pdf',
        });
      }
    } catch (error) {
      // console.error('Error downloading or opening PDF:', error);
      Alert.alert('Error', 'Could not download or open the PDF file');
    } finally {
      setDownloadingId(null);
    }
  };

  const handleAttachmentClick = useCallback((url, title, status) => {
    if (!url) {
      Alert.alert('Error', 'No file URL provided');
      return;
    }

    // If status is 'requested', show a message instead of requesting again
    if (status === 'requested') {
      Alert.alert('Access Pending', 'Your access request is pending approval. Please wait for approval.');
      return;
    }

    const isPdf = url.toLowerCase().endsWith('.pdf');
    const isImageFile = /\.(jpg|jpeg|png|gif|webp)$/i.test(url);

    if (isPdf) {
      downloadAndOpenPdf(url, title);
    } else if (isImageFile) {
      setPreviewUri(transformUrl(url));
      setShowPreview(true);
    } else {
      const transformedUrl = transformUrl(url);
      WebBrowser.openBrowserAsync(transformedUrl);
    }
  }, []);

  const closePreview = useCallback(() => {
    setShowPreview(false);
    setPreviewUri(null);
  }, []);

  const handleEvidenceTab = useCallback(() => {
    if (typeof changeTab === 'function') {
      changeTab('tab1');
    }
  }, [changeTab]);

  // Get report permission status using the enum
  const getReportStatus = (reportId) => {
    const status = reportPermissions[reportId];

    // Validate status against enum values
    if (status && Object.values(ForensicReportAccessEnum).includes(status)) {
      return status;
    }

    // Default to no request if no valid status found (means no request has been made)
    return ForensicReportAccessEnum.NO_REQUEST;
  };



  const handlePermissionRequest = async (report) => {
    try {
      // console.log('Requesting access for report:', {
      //   userId,
      //   reportId: report._id
      // });

      const response = await apiService.accessReport(token, userId, report._id);
      // console.log('Access Report Response:', response);

      if (response.status === 'success') {
        // Set status to 'requested' using enum
        // Access should only be granted by admin approval, not immediately
        setReportPermissions(prev => ({
          ...prev,
          [report._id]: ForensicReportAccessEnum.REQUESTED
        }));
        Alert.alert(
          'Success',
          'Access request submitted successfully. Please wait for approval.',
          [{ text: 'OK' }]
        );
      } else {
        // console.log('Access Report Error Response:', response);
        Alert.alert(
          'Error',
          response.message || 'Failed to submit access request',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      // console.error('Error requesting access:', error);
      // console.log('Error details:', {
      //   message: error.message,
      //   response: error.response?.data
      // });
      Alert.alert(
        'Error',
        'Failed to request access to the report. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  // Function to refresh access status for all reports
  const refreshAccessStatus = async () => {
    if (!reports || reports.length === 0) {
      Alert.alert('Info', 'No reports to refresh');
      return;
    }

    try {
      setLoading(true); // Show loading state while refreshing

      // First refetch all forensic reports
      const result = await apiService.fetchForensicReports(token, caseid);
      
      if (result && result.status === 'success' && result.data) {
        setReports(result.data);
      }

      const updatedPermissions = {};

      for (const report of result.data) {
        try {
          const accessStatus = await apiService.checkReportAccessStatus(token, report._id);

          if (accessStatus?.status === 'success') {
            if (accessStatus?.data?.length > 0) {
              const latestAccess = accessStatus.data[accessStatus.data.length - 1];
              const accessValue = latestAccess.access;

              if (Object.values(ForensicReportAccessEnum).includes(accessValue)) {
                updatedPermissions[report._id] = accessValue;
              } else {
                updatedPermissions[report._id] = ForensicReportAccessEnum.NO_REQUEST;
              }
            } else {
              updatedPermissions[report._id] = ForensicReportAccessEnum.NO_REQUEST;
            }
          } else {
            updatedPermissions[report._id] = ForensicReportAccessEnum.NO_REQUEST;
          }
        } catch (error) {
          updatedPermissions[report._id] = reportPermissions[report._id] || ForensicReportAccessEnum.NO_REQUEST;
        }
      }

      setReportPermissions(updatedPermissions);
    } catch (error) {
      Alert.alert(
        'Error',
        'Failed to refresh data. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false); // Hide loading state
    }
  };

  const renderReportMedia = useCallback((url) => {
    if (!url) return <View style={styles.evidenceImage} />;

    const transformedUrl = transformUrl(url);
    const isDocument = /\.(pdf|doc|docx|txt|xls|xlsx)$/i.test(url);
    const isPdf = /\.pdf$/i.test(url);

    if (isPdf) {
      return (
        <View style={styles.evidenceImage}>
          <Image
            source={require('../../../../assets/images/pdf-icon.png')}
            style={styles.pdfThumbnail}
            resizeMode="cover"
          />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>PDF</Text>
          </View>
        </View>
      );
    } else if (isDocument) {
      return (
        <View style={styles.evidenceImage}>
          <MaterialCommunityIcons 
            name="file-document" 
            size={30} 
            color="#FFFFFF" 
            style={styles.playIcon} 
          />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>DOC</Text>
          </View>
        </View>
      );
    } else {
      return (
        <Image
          source={{ uri: transformedUrl }}
          style={styles.evidenceImage}
          defaultSource={require('../../../../assets/images/small_satya_smadhanLogo.png')}
        />
      );
    }
  }, []);

  const renderItem = useCallback(({ item }) => {
    switch (item.type) {
      case 'details':
        return (
          <View style={styles.detailsContainer}>
            <View style={styles.segmentedControlContainer}>
              <View style={styles.segmentedControl}>
                <TouchableOpacity
                  style={styles.segmentButton}
                  onPress={handleEvidenceTab}
                >
                  <MaterialCommunityIcons name="file-document-multiple-outline" size={18} color="#666666" />
                  <Text style={styles.segmentText}>Evidences</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.segmentButton, styles.activeSegment]}
                >
                  <MaterialCommunityIcons name="clipboard-text-outline" size={18} color="#FFFFFF" />
                  <Text style={styles.activeSegmentText}>Reports</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        );
      case 'reports':
        return (
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Forensic Reports</Text>
              {reports && reports.length > 0 && (
                <TouchableOpacity
                  style={styles.refreshButton}
                  onPress={refreshAccessStatus}
                >
                  <MaterialCommunityIcons name="refresh" size={16} color="#0B36A1" />
                  {/* <Text style={styles.refreshButtonText}>Refresh</Text> */}
                </TouchableOpacity>
              )}
            </View>
            {reports && reports.length > 0 ? (
              reports.map((report, index) => {
                if (!report) return null;

                const labName = report.forensicRequestId?.labId?.name || 'Unknown Lab';
                const status = getReportStatus(report._id);

                return (
                  <View
                    key={report._id || index}
                    style={styles.evidenceCard}
                  >
                    <View style={styles.evidenceDetails}>
                      <View style={styles.titleContainer}>
                        <Text style={styles.evidenceTitle}>{report.title || 'Untitled Report'}</Text>
                        {/* Commented out Access Granted badge since button shows the status */}
                        {status !== ForensicReportAccessEnum.GRANTED && (
                          <View style={[
                            styles.statusBadge,
                            status === ForensicReportAccessEnum.REQUESTED ? styles.requestedBadge :
                            status === ForensicReportAccessEnum.PENDING ? styles.pendingBadge :
                            status === ForensicReportAccessEnum.REJECTED ? styles.rejectedBadge :
                            status === ForensicReportAccessEnum.NO_REQUEST ? styles.noRequestBadge :
                            styles.deniedBadge
                          ]}>
                            <Text style={styles.statusText}>
                              {status === ForensicReportAccessEnum.REQUESTED ? 'Access Requested' :
                               status === ForensicReportAccessEnum.PENDING ? 'Pending Approval' :
                               status === ForensicReportAccessEnum.REJECTED ? 'Access Rejected' :
                               status === ForensicReportAccessEnum.NO_REQUEST ? 'Need Access' :
                               'No Access'}
                            </Text>
                          </View>
                        )}
                      </View>
                      {report.description && (
                        <Text style={styles.evidenceDescription} numberOfLines={2}>
                          {report.description}
                        </Text>
                      )}
                      <Text style={styles.labName}>
                        Lab: {labName}
                      </Text>
                      <Text style={styles.reportDate}>
                        {new Date(report.createdAt).toLocaleDateString()}
                      </Text>
                    </View>
                    <View style={styles.attachmentsContainer}>
                      {report.attachmentUrl && report.attachmentUrl.length > 0 ? (
                        report.attachmentUrl.map((url, idx) => (
                          <TouchableOpacity
                            key={idx}
                            style={styles.attachmentItem}
                            onPress={() => {
                              if (status === ForensicReportAccessEnum.GRANTED) {
                                // Only allow PDF access in granted state
                                handleAttachmentClick(url, report.title, status);
                              } else if (status === ForensicReportAccessEnum.REQUESTED) {
                                Alert.alert(
                                  'Access Pending',
                                  'Your access request is pending approval. Please wait for admin approval.',
                                  [{ text: 'OK' }]
                                );
                              } else if (status === ForensicReportAccessEnum.PENDING) {
                                Alert.alert(
                                  'Access Under Review',
                                  'Your access request is being reviewed by the administrator.',
                                  [{ text: 'OK' }]
                                );
                              } else if (status === ForensicReportAccessEnum.REJECTED) {
                                Alert.alert(
                                  'Access Rejected',
                                  'Your access request has been rejected. You can request access again.',
                                  [
                                    { text: 'Cancel', style: 'cancel' },
                                    { text: 'Request Again', onPress: () => handlePermissionRequest(report) }
                                  ]
                                );
                              } else if (status === ForensicReportAccessEnum.NO_REQUEST) {
                                // No request has been made yet - show request option
                                handlePermissionRequest(report);
                              } else {
                                // Default case - fallback to request option
                                handlePermissionRequest(report);
                              }
                            }}
                          >
                            {renderReportMedia(url)}
                            {status === ForensicReportAccessEnum.GRANTED && (
                              <TouchableOpacity
                                style={styles.shareButton}
                                onPress={() => handleAttachmentClick(url, report.title, status)}
                                disabled={downloadingId === `${report.title}-${url}`}
                              >
                                {downloadingId === `${report.title}-${url}` ? (
                                  <ActivityIndicator size="small" color="#0B36A1" />
                                ) : (
                                  <MaterialCommunityIcons
                                    name={url.toLowerCase().endsWith('.pdf') ? "file-pdf-box" : "file-download"}
                                    size={24}
                                    color="#0B36A1"
                                  />
                                )}
                              </TouchableOpacity>
                            )}
                          </TouchableOpacity>
                        ))
                      ) : (
                        <Text style={styles.noEvidenceText}>No attachments found</Text>
                      )}
                    </View>
                    <TouchableOpacity
                      style={[
                        styles.requestAccessButton,
                        status === ForensicReportAccessEnum.GRANTED && styles.disabledRequestButton
                      ]}
                      onPress={() => {
                        if (status !== ForensicReportAccessEnum.GRANTED) {
                          handlePermissionRequest(report);
                        }
                      }}
                      disabled={status === ForensicReportAccessEnum.GRANTED}
                    >
                      <Text style={[
                        styles.requestAccessButtonText,
                        status === ForensicReportAccessEnum.GRANTED && styles.disabledRequestButtonText
                      ]}>
                        {status === ForensicReportAccessEnum.GRANTED ? 'Access Granted' :
                         status === ForensicReportAccessEnum.REJECTED ? 'Request Access Again' :
                         status === ForensicReportAccessEnum.REQUESTED ? 'Access Requested' :
                         status === ForensicReportAccessEnum.PENDING ? 'Pending Approval' :
                         status === ForensicReportAccessEnum.NO_REQUEST ? 'Request Access to View Report' :
                         'Request Access to View Report'}
                      </Text>
                    </TouchableOpacity>
                  </View>
                );
              })
            ) : (
              <Text style={styles.noEvidenceText}>No reports found</Text>
            )}
          </View>
        );
      default:
        return null;
    }
  }, [reports, handleEvidenceTab, handleAttachmentClick, renderReportMedia, downloadingId, reportPermissions, refreshAccessStatus]);

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="alert-circle-outline" size={48} color="#FF3B30" />
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchForensicReports}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.fullScreenLoader}>
        <ActivityIndicator size="large" color="#0B36A1" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={[{ type: 'details' }, { type: 'reports' }]}
        renderItem={renderItem}
        keyExtractor={(item, index) => `${item.type}-${index}`}
        contentContainerStyle={{ flexGrow: 1 }}
        initialNumToRender={2}
      />
      <Modal
        visible={showPreview}
        transparent={true}
        animationType="fade"
        onRequestClose={closePreview}
      >
        <PreviewComponent 
          uri={previewUri} 
          onClose={closePreview} 
        />
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  detailsContainer: {
    paddingHorizontal: '2%',
    paddingVertical: 10,
  },
  sectionContainer: {
    marginTop: 10,
  },
  sectionTitle: {
    textAlign: 'left',
    color: Colors.primary,
    fontWeight: '500',
    fontSize: 18,
    fontFamily: 'Roboto_bold',
    flex: 1,
  },
  evidenceCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginHorizontal: '2%',
    marginBottom: 24,
    padding: 12,
    borderRadius: 10,
  },
  evidenceDetails: {
    marginBottom: 12,
  },
  evidenceTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: Colors.black,
    marginBottom: 4,
  },
  evidenceDescription: {
    fontSize: 12,
    color: Colors.lightText,
    marginBottom: 8,
    fontFamily: 'Roboto',
  },
  labName: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '500',
    marginTop: 4,
    fontFamily: 'Roboto',
  },
  reportDate: {
    fontSize: 12,
    color: Colors.lightText,
    marginTop: 4,
    fontFamily: 'Roboto',
  },
  attachmentsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  attachmentItem: {
    width: 100,
    marginRight: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  evidenceImage: {
    height: 80,
    width: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  pdfThumbnail: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  shareButton: {
    padding: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  segmentedControlContainer: {
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  segmentedControl: {
    flexDirection: 'row',
    borderRadius: 30,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: '#E8E8E8',
  },
  segmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    justifyContent: 'center',
    width: 150,
  },
  activeSegment: {
    backgroundColor: Colors.green,
    borderRadius: 30,
  },
  segmentText: {
    color: '#666666',
    marginLeft: 8,
    fontWeight: '500',
  },
  activeSegmentText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontWeight: '500',
  },
  noEvidenceText: {
    textAlign: 'center',
    color: Colors.lightText,
    fontStyle: 'italic',
    marginTop: 20,
    marginBottom: 40,
    fontFamily: 'Roboto',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  errorText: {
    color: '#FF3B30',
    marginVertical: 16,
    textAlign: 'center',
    fontSize: 16,
  },
  retryButton: {
    backgroundColor: '#0B36A1',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  fullScreenLoader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  videoLabel: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
    fontFamily: 'Roboto',
  },
  playIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -15,
    marginTop: -15,
    zIndex: 10,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  deniedBadge: {
    backgroundColor: '#FF3B30', // Red for denied/no access
  },
  requestedBadge: {
    backgroundColor: '#FF9500', // Orange for requested status
  },
  pendingBadge: {
    backgroundColor: '#007AFF', // Blue for pending status
  },
  rejectedBadge: {
    backgroundColor: '#FF3B30', // Red for rejected status
  },
  noRequestBadge: {
    backgroundColor: '#8E8E93', // Gray for no request status
  },
  grantedBadge: {
    backgroundColor: Colors.green, // Green for granted status
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  requestAccessButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 30,
    alignSelf: 'center',
    marginTop: 8,
  },
  disabledRequestButton: {
    backgroundColor: Colors.disabled,
  },
  requestAccessButtonText: {
    color: Colors.background,
    fontSize: 12,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  disabledRequestButtonText: {
    color: Colors.lightText,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 14,
    marginHorizontal: '3%',
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#0B36A1',
    backgroundColor: '#F8F9FA',
    height: 28,
  },
  refreshButtonText: {
    color: '#0B36A1',
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
    fontFamily: 'Roboto',
  },
});

export default React.memo(Tab2);