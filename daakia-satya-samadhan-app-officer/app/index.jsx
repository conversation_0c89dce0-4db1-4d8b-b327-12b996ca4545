import { useEffect, useState } from 'react';
import { useRouter } from 'expo-router';
import { useAuth } from '../context/auth-context';
import LoadingScreen from '../components/Larges/LoadingScreen';

export default function Index() {
  const { user, selectedRole, isLoading, updateSelectedRole } = useAuth();
  const [showLoader, setShowLoader] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowLoader(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!isLoading && !showLoader) {
      if (!user || !user.token) {
        router.replace('/(auth)/welcome');
        return;
      }

      if (user.category !== 'police') {
        router.replace('/(auth)/welcome');
        return;
      }

      const validRoles = ['officer', 'dispatcher'];
      const hasValidRole = user.roles?.some(role => validRoles.includes(role));

      if (selectedRole === 'it-admin') {
        router.replace('/(auth)/it_adminNotallow');
        return;
      }

      if (!hasValidRole) {
        router.replace('/(auth)/welcome');
        return;
      }

      // Handle role-based routing
      if (selectedRole === 'officer') {
        router.replace('/(officer)');
        return;
      }

      if (selectedRole === 'dispatcher') {
        router.replace('/(dispatcher)');
        return;
      }

      // If no role is selected, default to the first valid role
      const defaultRole = user.roles.find(role => validRoles.includes(role));
      if (defaultRole) {
        updateSelectedRole(defaultRole);
        router.replace(`/(${defaultRole})`);
      } else {
        router.replace('/(auth)/welcome');
      }
    }
  }, [isLoading, showLoader, user, selectedRole, router, updateSelectedRole]);

  if (isLoading || showLoader) {
    return (
      <LoadingScreen
        loadingText="Syncing data with server..."
        primaryColor="#0a34a1"
        secondaryColor="#fff"
      />
    );
  }

  return null;
}
