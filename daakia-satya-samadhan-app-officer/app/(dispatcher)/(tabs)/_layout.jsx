import React from 'react';
import { Tabs } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import DispatcherCustomHeader from '../../../components/DispatcherComponets/DispatcherCustomHeader';
import { router } from 'expo-router';
const TabsLayoutDispatcher = () => {
    const handleNotificationPress = () => {
      router.push('(screensDispatchers)/notificationsDispacher');
    };

  const getTitle = (routeName) => {
    switch (routeName) {
      case 'index':
        return 'Dispatcher Home';
      case 'profile':
        return 'Profile';
      case 'settings':
        return 'Settings';
      default:
        return 'App';
    }
  };

  return (
    <Tabs
      screenOptions={({ route }) => ({
        tabBarActiveTintColor: '#0546a1',
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          backgroundColor: '#f8f8f8',
          borderTopWidth: 1,
          borderTopColor: '#e7e7e7',
        },
        header: () => (
          <DispatcherCustomHeader
            title={getTitle(route.name)}
            notificationHandler={handleNotificationPress}  // Notification everywhere
            showSearchIcon={false}                         // No search icon
            showNotificationIcon={true}                    // Notification icon everywhere
            notificationIconColor="#979797"                // Notification icon color
            borderBottomColor="#D8D8D8"                   // Border color for all
          />
        ),
      })}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Dispatcher Home',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="home" size={size} color={color} />
          ),
          tabBarLabel: 'Home',
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="person" size={size} color={color} />
          ),
          tabBarLabel: 'Profile',
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: 'Settings',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="settings" size={size} color={color} />
          ),
          tabBarLabel: 'Settings',
        }}
      />
    </Tabs>
  );
};

export default TabsLayoutDispatcher;