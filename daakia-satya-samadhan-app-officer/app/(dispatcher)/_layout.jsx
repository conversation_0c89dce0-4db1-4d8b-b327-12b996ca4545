import React from 'react';
import { Safe<PERSON>reaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { Stack } from 'expo-router';
import CustomStackHeader from '../../components/Larges/CustomStackHeader';



export default function MainLayoutDispatchers() {
  return (
    <SafeAreaProvider>
      <SafeAreaView style={{ flex: 1 }}>
        <Stack>

        <Stack.Screen
            name="(tabs)"
            options={{
              headerShown: false, 
            }}
          />
         
            <Stack.Screen
            name="(screensDispatchers)/dispatcherRecentCases"
             options={{
                          header: () => <CustomStackHeader title="View Packages " 
                          showBackButton={true}
                          backButtonColor="#979797"
                          showSearchIcon={false}
                          onSearchPress={() => console.log('Search Recent Cases')}
                          showNotificationIcon={false}
                          showBorder = {true} 
                          borderColor = '#D8D8D8'
                          />, 
                        }}
          />
             <Stack.Screen
            name="(screensDispatchers)/dipatchQr"
            options={{
              headerShown: false, 
              
            }}
          />
             <Stack.Screen
            name="(screensDispatchers)/ForensicRequestDetails"
            options={{
              header: () => <CustomStackHeader title="Case Evidances"
              showBackButton={true}
              backButtonColor="#979797"
              showSearchIcon={false}
              onSearchPress={() => console.log('Search Recent Cases')}
              showNotificationIcon={false}
              showBorder = {true} 
              borderColor = '#D8D8D8' />, 
            }}
          />

          <Stack.Screen
            name="(screensDispatchers)/notificationsDispacher"
            options={{
              header: () => <CustomStackHeader title="Notifications"
              showBackButton={true}
              backButtonColor="#979797"
              showSearchIcon={false}
              onSearchPress={() => console.log('Search Recent Cases')}
              showNotificationIcon={false}
              showBorder = {true} 
              borderColor = '#D8D8D8' />, 
            }}
          />

       
        </Stack>
      </SafeAreaView>
    </SafeAreaProvider>
  );
}