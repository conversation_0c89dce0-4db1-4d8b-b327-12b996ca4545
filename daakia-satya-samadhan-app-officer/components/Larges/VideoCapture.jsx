import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform, Alert, Linking } from 'react-native';
import { Camera, CameraType, FlashMode } from 'expo-camera';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

const VideoCapture = () => {
  const [hasPermission, setHasPermission] = useState(null);
  const [type, setType] = useState(CameraType.back);
  const [flashMode, setFlashMode] = useState(FlashMode.off);
  const [isRecording, setIsRecording] = useState(false);
  const [videoUri, setVideoUri] = useState(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const cameraRef = useRef(null);
  const recordingInterval = useRef(null);

  useEffect(() => {
    let isMounted = true;
    (async () => {
      try {
        const { status: cameraStatus } = await Camera.requestCameraPermissionsAsync();
        const { status: audioStatus } = await Camera.requestMicrophonePermissionsAsync();
        if (isMounted) {
          setHasPermission(cameraStatus === 'granted' && audioStatus === 'granted');
        }
      } catch (error) {
        console.error('Permission request failed:', error);
        if (isMounted) setHasPermission(false);
      }
    })();
    return () => { isMounted = false; };
  }, []);

  useEffect(() => {
    if (isRecording) {
      recordingInterval.current = setInterval(() => {
        setRecordingTime(t => t + 1);
      }, 1000);
    } else {
      clearInterval(recordingInterval.current);
      setRecordingTime(0);
    }
    return () => clearInterval(recordingInterval.current);
  }, [isRecording]);

  const startRecording = async () => {
    try {
      if (cameraRef.current) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        setIsRecording(true);
        const video = await cameraRef.current.recordAsync({
          quality: Camera.Constants.VideoQuality['720p'],
          maxDuration: 30,
          mute: false,
        });
        setVideoUri(video.uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to start recording');
      setIsRecording(false);
    }
  };

  const stopRecording = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    cameraRef.current?.stopRecording();
    setIsRecording(false);
  };

  const toggleFlash = () => {
    setFlashMode(current => {
      const modes = [FlashMode.off, FlashMode.on, FlashMode.auto];
      return modes[(modes.indexOf(current) + 1) % modes.length];
    });
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (hasPermission === null) return <View style={styles.container} />;
  if (hasPermission === false) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>
          Camera and microphone access is required to use this feature.
        </Text>
        <TouchableOpacity 
          style={styles.settingsButton}
          onPress={() => Linking.openSettings()}
        >
          <Text style={styles.settingsButtonText}>Open Settings</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Camera 
        style={styles.camera} 
        type={type} 
        ref={cameraRef}
        flashMode={flashMode}
      >
        <View style={styles.header}>
          <View style={styles.timeContainer}>
            {isRecording && (
              <>
                <View style={styles.recordingDot} />
                <Text style={styles.recordingTime}>{formatTime(recordingTime)}</Text>
              </>
            )}
          </View>
          
          <TouchableOpacity
            style={styles.flashButton}
            onPress={toggleFlash}
            disabled={isRecording}
          >
            <Ionicons
              name={flashMode === FlashMode.on ? 'ios-flash' : 'ios-flash-off'}
              size={28}
              color="white"
            />
          </TouchableOpacity>
        </View>

        <View style={styles.controls}>
          <TouchableOpacity
            style={styles.flipButton}
            onPress={() => setType(t => t === CameraType.back ? CameraType.front : CameraType.back)}
            disabled={isRecording}
          >
            <Ionicons
              name="ios-camera-reverse"
              size={32}
              color="white"
            />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={isRecording ? stopRecording : startRecording}
            style={[styles.recordButton, isRecording && styles.recording]}
          >
            {isRecording && <View style={styles.innerRecordButton} />}
          </TouchableOpacity>

          <View style={styles.rightControlPlaceholder} />
        </View>
      </Camera>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  camera: {
    flex: 1,
    justifyContent: 'space-between',
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.4)',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  recordingDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: 'red',
    marginRight: 8,
  },
  recordingTime: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  flashButton: {
    padding: 10,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
    paddingHorizontal: 30,
  },
  recordButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 4,
    borderColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  recording: {
    backgroundColor: 'red',
    borderColor: 'rgba(255,255,255,0.5)',
  },
  innerRecordButton: {
    width: 30,
    height: 30,
    borderRadius: 8,
    backgroundColor: 'white',
  },
  flipButton: {
    padding: 15,
  },
  rightControlPlaceholder: {
    width: 32,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'black',
  },
  permissionText: {
    color: 'white',
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 30,
  },
  settingsButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 10,
  },
  settingsButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default VideoCapture;