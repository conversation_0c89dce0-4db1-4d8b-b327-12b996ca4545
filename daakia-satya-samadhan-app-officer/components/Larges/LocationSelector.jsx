import React, { useCallback, useEffect, useState, useRef, useMemo } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ActivityIndicator, 
  TouchableOpacity, 
  Linking, 
  Platform, 
  Alert,
  Dimensions,
  Animated
} from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE, Circle } from 'react-native-maps';
import * as Location from 'expo-location';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { Colors } from '../../constants/colors';

const { width, height } = Dimensions.get('window');

const ERRORS = {
  PERMISSION_DENIED: 'Permission to access location was denied',
  SERVICES_DISABLED: 'Location services are disabled. Please enable them in settings.',
  FETCH_FAILED: 'Failed to get location. Please try again.',
  NO_SELECTION: 'No location selected',
  GEOCODE_FAILED: 'Failed to get address. Please try again.',
  TIMEOUT: 'Location fetch timed out. Please try again.',
  ACCURACY_LOW: 'Location accuracy is low. Please try again in an open area.',
};

const STYLES = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loaderText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
  mapContainer: {
    flex: 1,
    overflow: 'hidden',
    borderRadius: 10,
    margin: 10,
  },
  map: {
    flex: 1,
  },
  instructionCard: {
    backgroundColor: Colors.background,
    margin: 15,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  instructionText: {
    fontFamily: 'Roboto_bold',
    textAlign: 'center',
    fontSize: 16,
    color: Colors.black,
    marginBottom: 8,
  },
  locationAccuracy: {
    fontFamily: 'Roboto',
    textAlign: 'center',
    fontSize: 14,
    color: Colors.lightText,
    marginTop: 5,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 15,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderColor: '#eee',
    paddingBottom: Platform.OS === 'ios' ? 30 : 15,
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  confirmButton: {
    backgroundColor: Colors.primary,
  },
  retryButton: {
    backgroundColor: '#ff5733',
  },
  cancelButton: {
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  buttonText: {
    fontFamily: 'Roboto_bold',
    fontSize: 16,
    color: Colors.background,
    marginLeft: 8,
  },
  cancelButtonText: {
    color: Colors.lightText,
  },
  errorContainer: {
    backgroundColor: '#FEE2E2',
    margin: 15,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#DC2626',
  },
  errorTitle: {
    fontFamily: 'Roboto_bold',
    fontSize: 18,
    color: Colors.black,
    marginBottom: 8,
  },
  errorText: {
    fontFamily: 'Roboto',
    fontSize: 14,
    color: Colors.lightText,
    lineHeight: 20,
  },
  disabledButton: {
    backgroundColor: '#ccc',
    opacity: 0.7,
  },
  markerContainer: {
    alignItems: 'center',
  },
  markerIcon: {
    height: 35,
    width: 35,
  },
  markerPulse: {
    position: 'absolute',
    height: 50,
    width: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(11, 54, 161, 0.2)',
  },
  accuracyCircle: {
    position: 'absolute',
    borderWidth: 1,
    borderColor: 'rgba(11, 54, 161, 0.3)',
    backgroundColor: 'rgba(11, 54, 161, 0.1)',
  },
  mapControls: {
    position: 'absolute',
    right: 20,
    bottom: 100,
    backgroundColor: 'white',
    borderRadius: 30,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
  },
  controlButton: {
    width: 45,
    height: 45,
    borderRadius: 23,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 5,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
    padding: 20,
  },
  progressContainer: {
    width: '80%',
    marginVertical: 20,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#0B36A1',
    borderRadius: 4,
  },
  percentageText: {
    fontFamily: 'Roboto_bold',
    fontSize: 24,
    color: Colors.primary,
    marginTop: 10,
  },
  loadingPhaseText: {
    fontFamily: 'Roboto',
    fontSize: 16,
    color: Colors.lightText,
    marginTop: 8,
    textAlign: 'center',
  },
  mapPlaceholder: {
    height: 300,
    backgroundColor: '#E0E0E0',
    borderRadius: 10,
    marginBottom: 20,
  },
  permissionButton: {
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  permissionButtonText: {
    fontFamily: 'Roboto_bold',
    color: Colors.background,
    fontSize: 16,
    marginLeft: 8,
  },
});

const useCurrentLocation = ({ retryTrigger }) => {
  const [state, setState] = useState({
    region: null,
    coordinates: null,
    error: null,
    loading: true,
    accuracy: null,
    loadingProgress: 0,
    loadingPhase: 'Initializing...',
  });
  const locationSubscription = useRef(null);
  const locationTimeout = useRef(null);
  const isMountedRef = useRef(true);

  const clearLocationWatch = useCallback(() => {
    if (locationSubscription.current) {
      locationSubscription.current.remove();
      locationSubscription.current = null;
    }
    if (locationTimeout.current) {
      clearTimeout(locationTimeout.current);
      locationTimeout.current = null;
    }
  }, []);

  const updateLoadingState = useCallback((progress, phase) => {
    if (isMountedRef.current) {
      setState(prev => ({
        ...prev,
        loadingProgress: progress,
        loadingPhase: phase,
      }));
    }
  }, []);

  const fetchLocation = useCallback(async () => {
    try {
      // Quick permissions check without loading screen
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        throw new Error(ERRORS.PERMISSION_DENIED);
      }

      const isLocationEnabled = await Location.hasServicesEnabledAsync();
      if (!isLocationEnabled) {
        throw new Error(ERRORS.SERVICES_DISABLED);
      }

      // Get initial location immediately
      const initialLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      if (isMountedRef.current) {
        const region = {
          latitude: initialLocation.coords.latitude,
          longitude: initialLocation.coords.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };

        // Update state immediately
        setState({
          region,
          coordinates: initialLocation.coords,
          error: null,
          loading: false,
          accuracy: initialLocation.coords.accuracy,
          loadingProgress: 1,
          loadingPhase: 'Complete',
        });

        // Start watching location updates
        locationSubscription.current = await Location.watchPositionAsync(
          {
            accuracy: Location.Accuracy.High,
            timeInterval: 3000,
            distanceInterval: 10,
          },
          (location) => {
            if (isMountedRef.current) {
              setState(prev => ({
                ...prev,
                region: {
                  latitude: location.coords.latitude,
                  longitude: location.coords.longitude,
                  latitudeDelta: prev.region?.latitudeDelta || 0.01,
                  longitudeDelta: prev.region?.longitudeDelta || 0.01,
                },
                coordinates: location.coords,
                accuracy: location.coords.accuracy,
              }));
            }
          }
        );
      }

    } catch (error) {
      if (isMountedRef.current) {
        setState(prev => ({
          ...prev,
          error: error.message.includes('timeout') ? ERRORS.TIMEOUT : error.message,
          loading: false,
        }));
      }
    }
  }, [clearLocationWatch]);

  useEffect(() => {
    isMountedRef.current = true;
    fetchLocation();
    return () => {
      isMountedRef.current = false;
      clearLocationWatch();
    };
  }, [fetchLocation, retryTrigger]);

  return { ...state, retry: fetchLocation };
};

const LocationSelector = React.memo(({ onClose, onConfirm }) => {
  const [retryTrigger, setRetryTrigger] = useState(0);
  const { 
    region, 
    coordinates, 
    error: locationError, 
    loading, 
    retry, 
    accuracy,
    loadingProgress,
    loadingPhase 
  } = useCurrentLocation({ retryTrigger });
  const [confirmError, setConfirmError] = useState(null);
  const [mapType, setMapType] = useState('standard');
  const [zoomLevel, setZoomLevel] = useState(0.01);
  const mapRef = useRef(null);
  const pulseAnim = useRef(new Animated.Value(0.5)).current;

  // Start pulse animation
  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 0.5,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, [pulseAnim]);

  // Reset when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      return () => {
        setConfirmError(null);
      };
    }, [])
  );

  // Memoize handlers
  const handleRetry = useCallback(() => {
    setConfirmError(null);
    setRetryTrigger(prev => prev + 1);
  }, []);

  const handleConfirm = useCallback(async () => {
    if (!coordinates) {
      setConfirmError(ERRORS.NO_SELECTION);
      return;
    }

    try {
      const address = await Location.reverseGeocodeAsync(coordinates);
      const addr = address[0] || {};
      
      onConfirm({
        address1: addr.street || addr.name || `${coordinates.latitude}, ${coordinates.longitude}`,
        city: addr.city || addr.subregion || 'Unknown City',
        state: addr.region || addr.country || 'Unknown Region',
        pincode: addr.postalCode || '',
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
        accuracy: accuracy,
        fullAddress: [addr.street, addr.city, addr.region, addr.country, addr.postalCode]
          .filter(Boolean).join(', ')
      });
      onClose();
    } catch (error) {
      setConfirmError(ERRORS.GEOCODE_FAILED);
      console.error('Geocode error:', error);
    }
  }, [coordinates, onClose, onConfirm, accuracy]);

  // 3. Optimize map controls
  const mapControls = useMemo(() => ({
    toggleMapType: () => setMapType(type => type === 'standard' ? 'satellite' : 'standard'),
    zoomIn: () => {
      const newZoom = Math.max(0.001, zoomLevel / 2);
      setZoomLevel(newZoom);
      mapRef.current?.animateToRegion({
        ...region,
        latitudeDelta: newZoom,
        longitudeDelta: newZoom,
      }, 300);
    },
    zoomOut: () => {
      const newZoom = Math.min(0.1, zoomLevel * 2);
      setZoomLevel(newZoom);
      mapRef.current?.animateToRegion({
        ...region,
        latitudeDelta: newZoom,
        longitudeDelta: newZoom,
      }, 300);
    },
    recenterMap: () => {
      if (mapRef.current && coordinates) {
        mapRef.current.animateToRegion({
          latitude: coordinates.latitude,
          longitude: coordinates.longitude,
          latitudeDelta: zoomLevel,
          longitudeDelta: zoomLevel,
        }, 300);
      }
    }
  }), [zoomLevel, region, coordinates]);

  const openSettings = () => {
    if (Platform.OS === 'ios') {
      Linking.openURL('app-settings:').catch(() =>
        Alert.alert('Settings Error', 'Unable to open settings. Please enable location services manually.')
      );
    } else {
      Linking.openSettings().catch(() =>
        Alert.alert('Settings Error', 'Unable to open settings. Please enable location services manually.')
      );
    }
  };

  const getAccuracyDescription = () => {
    if (!accuracy) return '';
    if (accuracy < 10) return 'Excellent';
    if (accuracy < 50) return 'Good';
    if (accuracy < 100) return 'Fair';
    return 'Poor';
  };

  // Simplified loading view
  const renderLoading = () => (
    <View style={STYLES.loaderContainer}>
      <ActivityIndicator size="large" color={Colors.primary} />
      <Text style={STYLES.loaderText}>Getting your location...</Text>
    </View>
  );

  // Enhanced map container styles
  const enhancedStyles = {
    mapContainer: {
      ...STYLES.mapContainer,
      borderRadius: 15,
      margin: 15,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    instructionCard: {
      ...STYLES.instructionCard,
      marginTop: 10,
      marginBottom: 5,
    },
    accuracyCircle: {
      ...STYLES.accuracyCircle,
      borderColor: Colors.primary + '40',
      backgroundColor: Colors.primary + '20',
    },
  };

  return (
    <View style={STYLES.container}>
      {loading ? (
        renderLoading()
      ) : region && !locationError ? (
        <>
          <View style={enhancedStyles.mapContainer}>
            <MapView
              ref={mapRef}
              style={STYLES.map}
              provider={Platform.OS === 'ios' ? undefined : PROVIDER_GOOGLE}
              initialRegion={{
                ...region,
                latitudeDelta: zoomLevel,
                longitudeDelta: zoomLevel,
              }}
              mapType={mapType}
              showsUserLocation={true}
              showsMyLocationButton={false}
              showsCompass={true}
              showsScale={true}
              rotateEnabled={true}
              scrollEnabled={true}
              zoomEnabled={true}
              pitchEnabled={true}
              onMapReady={mapControls.recenterMap}
            >
              {coordinates && (
                <Marker coordinate={coordinates} title="Your Current Location">
                  <View style={STYLES.markerContainer}>
                    <Animated.View 
                      style={[
                        STYLES.markerPulse,
                        {
                          transform: [{ scale: pulseAnim }],
                          opacity: pulseAnim.interpolate({
                            inputRange: [0.5, 1],
                            outputRange: [0.3, 0]
                          })
                        }
                      ]} 
                    />
                    <MaterialIcons name="location-pin" size={35} color="#0B36A1" />
                  </View>
                </Marker>
              )}
              {coordinates && accuracy && accuracy < 200 && (
                <Circle
                  center={coordinates}
                  radius={accuracy}
                  strokeWidth={1}
                  strokeColor="rgba(11, 54, 161, 0.5)"
                  fillColor="rgba(11, 54, 161, 0.15)"
                />
              )}
            </MapView>
            
            <View style={STYLES.mapControls}>
              <TouchableOpacity style={STYLES.controlButton} onPress={mapControls.toggleMapType}>
                <MaterialIcons name={mapType === 'standard' ? 'satellite' : 'map'} size={24} color="#555" />
              </TouchableOpacity>
              <TouchableOpacity style={STYLES.controlButton} onPress={mapControls.zoomIn}>
                <MaterialIcons name="add" size={24} color="#555" />
              </TouchableOpacity>
              <TouchableOpacity style={STYLES.controlButton} onPress={mapControls.zoomOut}>
                <MaterialIcons name="remove" size={24} color="#555" />
              </TouchableOpacity>
              <TouchableOpacity style={STYLES.controlButton} onPress={mapControls.recenterMap}>
                <MaterialIcons name="my-location" size={24} color="#555" />
              </TouchableOpacity>
            </View>
          </View>
          
          <View style={enhancedStyles.instructionCard}>
            <Text style={enhancedStyles.instructionText}>
              This is your current location
            </Text>
            {accuracy && (
              <Text style={enhancedStyles.locationAccuracy}>
                Location Accuracy: {Math.round(accuracy)}m ({getAccuracyDescription()})
              </Text>
            )}
          </View>
          
          {confirmError && (
            <View style={STYLES.errorContainer}>
              <Text style={STYLES.errorTitle}>Location Services Required</Text>
              <Text style={STYLES.errorText}>
                {confirmError}. Please enable location services to continue using this feature.
              </Text>
              <TouchableOpacity
                style={STYLES.permissionButton}
                onPress={openSettings}
              >
                <Ionicons name="settings-outline" size={20} color={Colors.background} />
                <Text style={STYLES.permissionButtonText}>
                  Open Settings
                </Text>
              </TouchableOpacity>
            </View>
          )}
          
          <View style={STYLES.buttonContainer}>
            <TouchableOpacity
              style={[STYLES.button, STYLES.cancelButton]}
              onPress={onClose}
              accessible={true}
              accessibilityLabel="Cancel"
              accessibilityHint="Closes the location selector without saving"
            >
              <Ionicons name="close" size={20} color="#555" />
              <Text style={[STYLES.buttonText, STYLES.cancelButtonText]}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[STYLES.button, STYLES.confirmButton, !coordinates && STYLES.disabledButton]}
              onPress={handleConfirm}
              disabled={!coordinates}
              accessible={true}
              accessibilityLabel="Confirm location"
              accessibilityHint="Confirms your current location on the map"
            >
              <Ionicons name="checkmark" size={20} color="white" />
              <Text style={STYLES.buttonText}>Confirm</Text>
            </TouchableOpacity>
          </View>
        </>
      ) : (
        <View style={STYLES.loaderContainer}>
          <View style={STYLES.errorContainer}>
            <Text style={STYLES.errorTitle}>Location Services Required</Text>
            <Text style={STYLES.errorText}>
              {locationError}. Please enable location services to continue using this feature.
            </Text>
            <TouchableOpacity
              style={STYLES.permissionButton}
              onPress={openSettings}
            >
              <Ionicons name="settings-outline" size={20} color={Colors.background} />
              <Text style={STYLES.permissionButtonText}>
                Open Settings
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
});

export default LocationSelector;