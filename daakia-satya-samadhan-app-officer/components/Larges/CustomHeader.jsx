import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useNavigation } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function CustomHeader({
  title,
  showMenu = true, 
  menuColor = '#9e9e9e', 
  showSearch = false, 
  searchHandler, 
  searchIconName = 'search-outline', 
  searchIconColor = '#9e9e9e', 
  showNotification = false, 
  notificationHandler, 
  notificationIconName = 'notifications-outline', 
  notificationIconColor = '#9e9e9e', 
  headerBackgroundColor = '#fff', 
  titleColor = '#333', 
}) {
  const navigation = useNavigation();

  return (
    <View style={[styles.headerContainer, { backgroundColor: headerBackgroundColor }]}>
      {/* Left Side: Menu Button + Title */}
      <View style={styles.leftContainer}>
        {showMenu && (
          <TouchableOpacity onPress={() => navigation.openDrawer()}>
            <Ionicons name="menu-outline" size={26} color={menuColor} />
          </TouchableOpacity>
        )}
        <Text style={[styles.title, { color: titleColor }]}>{title}</Text>
      </View>

      {/* Right Icons (Search + Notifications) */}
      <View style={styles.rightIcons}>
        {showSearch && searchHandler && (
          <TouchableOpacity onPress={searchHandler}>
            <Ionicons name={searchIconName} size={22} color={searchIconColor} />
          </TouchableOpacity>
        )}
        {showNotification && notificationHandler && (
          <TouchableOpacity onPress={notificationHandler}>
            <Ionicons name={notificationIconName} size={22} color={notificationIconColor} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 25,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#D8D8D8',
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 15,
  },
  rightIcons: {
    flexDirection: 'row',
    gap: 23,
  },
});