import React, { useCallback } from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Colors } from '../../constants/colors';
import { transformUrl } from '../../utils/transformUrl';

const EvidencesCards = ({
  evidences,
  isSelectionMode,
  selectedEvidenceIds,
  isSubmitting,
  labsData,
  departmentsData,
  styles,
  onEvidenceClick,
  onLongPress,
  onShareButtonClick,
}) => {
  // Helper function to get lab and department names from evidence data
  const getLabsAndDepartmentsFromEvidence = useCallback((evidence) => {
    // First check if evidence has lab_department array with populated data
    if (evidence.lab_department && evidence.lab_department.length > 0) {
      // Sort by priority (ascending: 1, 2, 3...) and get unique labs and departments
      const sortedLabDept = evidence.lab_department.sort((a, b) => a.priority - b.priority);
      
      // Get unique labs (by priority)
      const uniqueLabs = [];
      const seenLabIds = new Set();
      sortedLabDept.forEach(item => {
        if (!seenLabIds.has(item.labId._id)) {
          uniqueLabs.push(item.labId.name);
          seenLabIds.add(item.labId._id);
        }
      });
      
      // Get unique departments (by priority)
      const uniqueDepartments = [];
      const seenDeptIds = new Set();
      sortedLabDept.forEach(item => {
        if (!seenDeptIds.has(item.labDepartmentId._id)) {
          uniqueDepartments.push(item.labDepartmentId.name);
          seenDeptIds.add(item.labDepartmentId._id);
        }
      });
      
      return {
        labName: uniqueLabs.join(', '),
        departmentName: uniqueDepartments.join(', '),
        hasLabInfo: true
      };
    }
    
    // Fallback to old method for backward compatibility
    if (evidence.labId && evidence.labDepartmentId) {
      const labName = typeof evidence.labId === 'object' && evidence.labId?.name 
        ? evidence.labId.name 
        : labsData.find(lab => lab._id === evidence.labId)?.name || 'Unknown Lab';
        
      const departmentName = typeof evidence.labDepartmentId === 'object' && evidence.labDepartmentId?.name
        ? evidence.labDepartmentId.name
        : departmentsData[evidence.labDepartmentId]?.name || 'Unknown Department';
        
      return {
        labName,
        departmentName,
        hasLabInfo: true
      };
    }
    
    return {
      labName: null,
      departmentName: null,
      hasLabInfo: false
    };
  }, [labsData, departmentsData]);

  const renderEvidenceMedia = useCallback((url) => {
    if (!url) return <View style={styles.evidenceImage} />;
    
    const transformedUrl = transformUrl(url);
    const isVideo = /\.(mp4|mov|avi|wmv|flv|webm|mkv)$/i.test(url);

    if (isVideo) {
      return (
        <View style={styles.evidenceImage}>
          <MaterialCommunityIcons name="play-circle" size={30} color={Colors.background} style={styles.playIcon} />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>VIDEO</Text>
          </View>
        </View>
      );
    } else {
      return (
        <Image
          source={{ uri: transformedUrl }}
          style={styles.evidenceImage}
          defaultSource={require('../../assets/images/small_satya_smadhanLogo.png')}
        />
      );
    }
  }, [styles]);

  return (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>
        {isSelectionMode
          ? `Evidences (${selectedEvidenceIds.length} selected)`
          : 'Evidences'}
      </Text>
      {evidences && evidences.length > 0 ? (
        evidences.map((evidence, index) => {
          if (!evidence) return null;

          const firstAttachment = evidence.attachmentUrl?.[0] || null;
          const labDeptInfo = getLabsAndDepartmentsFromEvidence(evidence);
          const hasLabInfo = labDeptInfo.hasLabInfo;
          const isSelected = selectedEvidenceIds.includes(evidence._id);

          return (
            <TouchableOpacity
              key={evidence._id || index}
              style={[
                styles.evidenceCard,
                isSelected && styles.selectedEvidenceCard
              ]}
              onPress={() => onEvidenceClick(evidence._id)}
              onLongPress={() => onLongPress(evidence._id, hasLabInfo)}
              delayLongPress={300}
              disabled={isSubmitting}
            >
              {isSelected && (
                <View style={styles.selectedIndicator}>
                  <MaterialCommunityIcons name="check-circle" size={24} color={Colors.primary} />
                </View>
              )}
              <View style={styles.imageContainer}>
                {renderEvidenceMedia(firstAttachment)}
              </View>
              <View style={styles.evidenceDetails}>
                <Text style={styles.evidenceTitle}>{evidence.title || 'Untitled Evidence'}</Text>
                <Text style={styles.evidenceLabel}>
                  Type: <Text style={styles.evidenceType}>{evidence.type || 'Unknown'}</Text>
                </Text>

                {labDeptInfo.labName && (
                  <Text style={styles.evidenceLabel}>
                    Lab: <Text style={styles.evidenceType}>
                      {labDeptInfo.labName}
                    </Text>
                  </Text>
                )}
                {labDeptInfo.departmentName && (
                  <Text style={styles.evidenceLabel}>
                    Department: <Text style={styles.evidenceType}>
                      {labDeptInfo.departmentName}
                    </Text>
                  </Text>
                )}
              </View>
              {!isSelectionMode && (
                <TouchableOpacity
                  style={styles.shareButton}
                  onPress={() => onShareButtonClick(evidence._id)}
                  disabled={isSubmitting}
                >
                  <MaterialCommunityIcons
                    name="share-circle"
                    size={24}
                    color={hasLabInfo ? "#4CAF50" : Colors.primary}
                  />
                </TouchableOpacity>
              )}
            </TouchableOpacity>
          );
        })
      ) : (
        <Text style={styles.noEvidenceText}>No evidences found</Text>
      )}
    </View>
  );
};

export default EvidencesCards;
