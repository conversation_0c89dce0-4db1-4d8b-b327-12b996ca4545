import React, { useRef, useEffect } from 'react';
import { Animated, Text, View, StyleSheet } from 'react-native';

const Loader = ({ prompt }) => {
  const loadingDotAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Fade in animation
    Animated.timing(fadeAnimation, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();

    // Animated loading dots
    Animated.loop(
      Animated.sequence([
        Animated.timing(loadingDotAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(loadingDotAnimation, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const dotsTranslate = loadingDotAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [-4, 4],
  });

  return (
    <Animated.View style={[styles.content, { opacity: fadeAnimation }]}>
      <View style={styles.logoContainer}>
        <View style={styles.logo} />
      </View>

      <View style={styles.messageContainer}>
        <Text style={styles.title}>Welcome</Text>
        <View style={styles.loadingTextContainer}>
          <Text style={styles.loadingText}>{prompt}</Text>
          <Animated.View style={[styles.dots, { transform: [{ translateX: dotsTranslate }] }]}>
            <Text style={styles.loadingText}>...</Text>
          </Animated.View>
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    marginBottom: 40,
  },
  logo: {
    width: 80,
    height: 80,
    backgroundColor: '#1a73e8',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  messageContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
  },
  loadingTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  dots: {
    width: 24,
    alignItems: 'flex-start',
  },
});

export default Loader;