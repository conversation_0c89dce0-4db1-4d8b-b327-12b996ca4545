import React, { useState } from 'react';
import { 
  View, 
  Image, 
  StyleSheet, 
  Alert, 
  Text, 
  TouchableOpacity, 
  ActivityIndicator 
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Ionicons } from '@expo/vector-icons';
import useUploadMedia from '../../hooks/useUploadMedia';

export default function ImagePickerUpload(props) {
  const { onUploadComplete } = props;
  const [image, setImage] = useState(null);
  const [uploadComplete, setUploadComplete] = useState(false);
  const { uploadMedia, isUploading, error } = useUploadMedia();

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Denied', 'Sorry, we need camera roll permissions to make this work!');
      return;
    }
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });
    if (!result.canceled) {
      setImage(result.assets[0].uri);
      setUploadComplete(false);
      handleUpload(result.assets[0].uri);
    }
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Denied', 'Sorry, we need camera permissions to take photos!');
      return;
    }
    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });
    if (!result.canceled) {
      setImage(result.assets[0].uri);
      setUploadComplete(false);
      handleUpload(result.assets[0].uri);
    }
  };

  const handleUpload = async (uri) => {
    const fileUrl = await uploadMedia(uri, 'image');
    
    if (fileUrl) {
      setUploadComplete(true);
      if (onUploadComplete) {
        onUploadComplete(fileUrl);
      }
    } else if (error) {
      Alert.alert('Upload Failed', error);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        {/* Left side - Image preview */}
        <View style={styles.previewContainer}>
          {image ? (
            <View style={styles.imageWrapper}>
              <Image source={{ uri: image }} style={styles.image} />
            </View>
          ) : (
            <View style={styles.placeholderContainer}>
              <Ionicons name="image-outline" size={40} color="#ccc" />
              <Text style={styles.placeholderText}>No image selected</Text>
            </View>
          )}
        </View>

        {/* Right side - Options */}
        <View style={styles.optionsContainer}>
          <Text style={styles.title}>Upload Image</Text>
          
          <TouchableOpacity 
            style={styles.button} 
            onPress={pickImage} 
            disabled={isUploading}
          >
            <Ionicons name="images" size={20} color="white" />
            <Text style={styles.buttonText}>Gallery</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.button} 
            onPress={takePhoto} 
            disabled={isUploading}
          >
            <Ionicons name="camera" size={20} color="white" />
            <Text style={styles.buttonText}>Camera</Text>
          </TouchableOpacity>

          {isUploading && (
            <View style={styles.statusContainer}>
              <ActivityIndicator size="small" color="#0055aa" />
              <Text style={styles.statusText}>Uploading...</Text>
            </View>
          )}

          {uploadComplete && !isUploading && (
            <View style={styles.statusContainer}>
              <Ionicons name="checkmark-circle" size={18} color="#00aa55" />
              <Text style={styles.successText}>Upload Complete</Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f5f5f5',
  },
  card: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 8,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
    height: 200,
  },
  previewContainer: {
    flex: 1.5,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fafafa',
    padding: 10,
  },
  imageWrapper: {
    width: '100%',
    height: '100%',
    padding: 8,
    borderRadius: 6,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
    borderRadius: 6,
  },
  placeholderContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    marginTop: 8,
    color: '#888',
    fontSize: 12,
  },
  optionsContainer: {
    flex: 1,
    padding: 15,
    backgroundColor: '#f9f9f9',
    justifyContent: 'flex-start',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0055aa',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  buttonText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 14,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  statusText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
  },
  successText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#00aa55',
    fontWeight: '500',
  },
});