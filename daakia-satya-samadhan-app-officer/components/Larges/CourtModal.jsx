import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  Alert
} from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Colors } from '../../constants/colors';
import { apiService } from '../../services/api';
import { useAuth } from '../../context/auth-context';

const CourtModal = ({ isVisible, onClose, onConfirmSelection, isSubmitting }) => {
  const { token } = useAuth();
  const [courts, setCourts] = useState([]);
  const [prosecutors, setProsecutors] = useState([]);
  const [selectedCourtId, setSelectedCourtId] = useState(null);
  const [selectedProsecutorId, setSelectedProsecutorId] = useState(null);
  const [currentStep, setCurrentStep] = useState('court'); // 'court' or 'prosecutor'
  const [isLoading, setIsLoading] = useState(true);
  const [isProsecutorLoading, setProsecutorLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (isVisible) {
      resetModal();
      fetchCourts();
    }
  }, [isVisible]);

  const resetModal = () => {
    setCurrentStep('court');
    setSelectedCourtId(null);
    setSelectedProsecutorId(null);
    setProsecutors([]);
    setError(null);
  };

  const fetchCourts = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const result = await apiService.fetchCourts();
      if (result?.data && Array.isArray(result.data)) {
        setCourts(result.data);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching courts:', err);
      setError(err.message || 'Failed to fetch courts');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchProsecutors = async (courtId) => {
    setProsecutorLoading(true);
    setError(null);
    try {
      const result = await apiService.fetchProsecutors(token, courtId);
      if (result?.data && Array.isArray(result.data)) {
        setProsecutors(result.data);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching prosecutors:', err);
      setError(err.message || 'Failed to fetch prosecutors');
    } finally {
      setProsecutorLoading(false);
    }
  };

  const handleCourtSelect = (courtId) => {
    setSelectedCourtId(courtId);
  };

  const handleProsecutorSelect = (prosecutorId) => {
    setSelectedProsecutorId(prosecutorId);
  };

  const handleCourtNext = async () => {
    if (!selectedCourtId) {
      Alert.alert('Selection Required', 'Please select a court before proceeding.');
      return;
    }

    // Fetch prosecutors for the selected court
    await fetchProsecutors(selectedCourtId);
    setCurrentStep('prosecutor');
  };

  const handleBackToCourt = () => {
    setCurrentStep('court');
    setSelectedProsecutorId(null);
    setProsecutors([]);
  };

  const handleSkipProsecutor = () => {
    const selectedCourt = courts.find(court => court._id === selectedCourtId);
    onConfirmSelection(selectedCourtId, selectedCourt?.name || 'Selected Court', null);
  };

  const handleConfirm = () => {
    if (currentStep === 'court') {
      handleCourtNext();
    } else {
      // Prosecutor step - confirm with selected prosecutor
      const selectedCourt = courts.find(court => court._id === selectedCourtId);
      const selectedProsecutor = prosecutors.find(prosecutor => prosecutor._id === selectedProsecutorId);
      onConfirmSelection(
        selectedCourtId,
        selectedCourt?.name || 'Selected Court',
        selectedProsecutorId,
        selectedProsecutor?.name || 'Selected Prosecutor'
      );
    }
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <View style={styles.headerLeft}>
              {currentStep === 'prosecutor' && (
                <TouchableOpacity onPress={handleBackToCourt} disabled={isSubmitting}>
                  <MaterialCommunityIcons name="arrow-left" size={24} color={Colors.black} />
                </TouchableOpacity>
              )}
              <Text style={styles.modalTitle}>
                {currentStep === 'court' ? 'Select Court' : 'Select Prosecutor (Optional)'}
              </Text>
            </View>
            <TouchableOpacity onPress={onClose} disabled={isSubmitting}>
              <MaterialCommunityIcons name="close" size={24} color={Colors.black} />
            </TouchableOpacity>
          </View>

          {currentStep === 'court' ? (
            // Court Selection Step
            isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={Colors.primary} />
                <Text style={styles.loadingText}>Loading courts...</Text>
              </View>
            ) : error ? (
              <View style={styles.errorContainer}>
                <MaterialCommunityIcons name="alert-circle-outline" size={48} color="#FF3B30" />
                <Text style={styles.errorText}>{error}</Text>
                <TouchableOpacity style={styles.retryButton} onPress={fetchCourts}>
                  <Text style={styles.retryButtonText}>Retry</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <ScrollView style={styles.courtsList}>
                {courts.length === 0 ? (
                  <Text style={styles.noDataText}>No courts available</Text>
                ) : (
                  courts.map((court) => (
                    <TouchableOpacity
                      key={court._id}
                      style={[
                        styles.courtItem,
                        selectedCourtId === court._id && styles.selectedCourt
                      ]}
                      onPress={() => handleCourtSelect(court._id)}
                      disabled={isSubmitting}
                    >
                      <View style={styles.courtInfo}>
                        <Text style={styles.courtName}>{court.name}</Text>
                        <Text style={styles.courtLocation}>{court.location}</Text>
                        <Text style={styles.courtType}>{court.type}</Text>
                      </View>
                      {selectedCourtId === court._id && (
                        <MaterialCommunityIcons name="check-circle" size={24} color={Colors.primary} />
                      )}
                    </TouchableOpacity>
                  ))
                )}
              </ScrollView>
            )
          ) : (
            // Prosecutor Selection Step
            isProsecutorLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={Colors.primary} />
                <Text style={styles.loadingText}>Loading prosecutors...</Text>
              </View>
            ) : error ? (
              <View style={styles.errorContainer}>
                <MaterialCommunityIcons name="alert-circle-outline" size={48} color="#FF3B30" />
                <Text style={styles.errorText}>{error}</Text>
                <TouchableOpacity style={styles.retryButton} onPress={() => fetchProsecutors(selectedCourtId)}>
                  <Text style={styles.retryButtonText}>Retry</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <ScrollView style={styles.courtsList}>
                <Text style={styles.optionalText}>
                  You can proceed without selecting a prosecutor or choose one from the list below:
                </Text>
                {prosecutors.length === 0 ? (
                  <Text style={styles.noDataText}>No prosecutors available for this court</Text>
                ) : (
                  prosecutors.map((prosecutor) => (
                    <TouchableOpacity
                      key={prosecutor._id}
                      style={[
                        styles.courtItem,
                        selectedProsecutorId === prosecutor._id && styles.selectedCourt
                      ]}
                      onPress={() => handleProsecutorSelect(prosecutor._id)}
                      disabled={isSubmitting}
                    >
                      <View style={styles.courtInfo}>
                        <Text style={styles.courtName}>{prosecutor.name}</Text>
                        <Text style={styles.courtLocation}>{prosecutor.emailId}</Text>
                        <Text style={styles.courtType}>{prosecutor.courtProfile?.designation || 'Prosecutor'}</Text>
                      </View>
                      {selectedProsecutorId === prosecutor._id && (
                        <MaterialCommunityIcons name="check-circle" size={24} color={Colors.primary} />
                      )}
                    </TouchableOpacity>
                  ))
                )}
              </ScrollView>
            )
          )}

          <View style={styles.buttonRow}>
            {currentStep === 'court' ? (
              <>
                <TouchableOpacity
                  style={[styles.cancelButton, isSubmitting && styles.disabledButton]}
                  onPress={onClose}
                  disabled={isSubmitting}
                >
                  <Text style={styles.buttonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.confirmButton,
                    (!selectedCourtId || isSubmitting) && styles.disabledButton
                  ]}
                  onPress={handleConfirm}
                  disabled={!selectedCourtId || isSubmitting}
                >
                  {isSubmitting ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={styles.buttonText}>Next</Text>
                  )}
                </TouchableOpacity>
              </>
            ) : (
              <>
                <TouchableOpacity
                  style={[styles.skipButton, isSubmitting && styles.disabledButton]}
                  onPress={handleSkipProsecutor}
                  disabled={isSubmitting}
                >
                  <Text style={styles.skipButtonText}>Skip & Submit</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.confirmButton,
                    (!selectedProsecutorId || isSubmitting) && styles.disabledButton
                  ]}
                  onPress={handleConfirm}
                  disabled={!selectedProsecutorId || isSubmitting}
                >
                  {isSubmitting ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Text style={styles.buttonText}>Submit</Text>
                  )}
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: Colors.background,
    borderRadius: 12,
    width: '90%',
    maxHeight: '80%',
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    paddingBottom: 12,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.black,
    fontFamily: 'Roboto_Bold',
    marginLeft: 8,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    height: 200,
  },
  loadingText: {
    marginTop: 12,
    color: Colors.lightText,
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    height: 200,
  },
  errorText: {
    marginTop: 12,
    color: '#FF3B30',
    fontSize: 14,
    textAlign: 'center',
    fontFamily: 'Roboto',
  },
  retryButton: {
    marginTop: 12,
    backgroundColor: Colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  retryButtonText: {
    color: Colors.background,
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  courtsList: {
    maxHeight: 500,
  },
  noDataText: {
    textAlign: 'center',
    color: Colors.lightText,
    fontSize: 16,
    padding: 20,
    fontStyle: 'italic',
    fontFamily: 'Roboto',
  },
  courtItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    marginBottom: 8,
    borderRadius: 6,
  },
  selectedCourt: {
    backgroundColor: 'rgba(11, 54, 161, 0.05)',
    borderRadius: 8,
 
  },
  courtInfo: {
    flex: 1,
  },
  courtName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
    fontFamily: 'Roboto_Bold',
  },
  courtLocation: {
    fontSize: 14,
    color: Colors.lightText,
    marginBottom: 2,
    fontFamily: 'Roboto',
  },
  courtType: {
    fontSize: 12,
    color: Colors.lightText,
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
    fontFamily: 'Roboto',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: Colors.lightText,
    padding: 12,
    borderRadius: 30,
    alignItems: 'center',
    marginRight: 8,
  },
  confirmButton: {
    flex: 1,
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 30,
    alignItems: 'center',
    marginLeft: 8,
  },
  disabledButton: {
    opacity: 0.7,
    backgroundColor: Colors.disabled,
  },
  buttonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  skipButton: {
    flex: 1,
    backgroundColor: Colors.lightText,
    padding: 12,
    borderRadius: 30,
    alignItems: 'center',
    marginRight: 8,
  },
  skipButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  optionalText: {
    fontSize: 14,
    color: Colors.lightText,
    textAlign: 'center',
    marginBottom: 16,
    fontStyle: 'italic',
    fontFamily: 'Roboto',
  }
});

export default CourtModal; 