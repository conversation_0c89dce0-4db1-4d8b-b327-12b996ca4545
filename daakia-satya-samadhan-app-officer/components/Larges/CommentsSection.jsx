import React, { useState } from 'react';
import { View, Text, TouchableOpacity, TextInput, Image, FlatList } from 'react-native';

const CommentsSection = ({
  comments = [],
  isSelectionMode = false,
  isSubmitting = false,
  styles,
  onAddComment,
  placeholder = "Type your comment here...",
  title = "Add Comment",
  commentsTitle = "Comments",
  currentUser = null,
  showAddComment = true,
  disabled = false,
}) => {
  const [commentText, setCommentText] = useState('');

  const handleAddComment = () => {
    if (commentText.trim() && onAddComment) {
      onAddComment(commentText.trim());
      setCommentText('');
    }
  };

  const renderComment = ({ item, index }) => (
    <View key={item.id || index} style={styles.commentItem}>
      <Image 
        style={styles.userAvatar}
        source={item.userAvatar ? { uri: item.userAvatar } : null}
        defaultSource={require('../../assets/images/small_satya_smadhanLogo.png')}
      />
      <View style={styles.commentContent}>
        <Text style={styles.userName}>
          {item.userName || 'Unknown User'} 
          {item.userRole && ` (${item.userRole})`}
        </Text>
        <Text style={styles.commentText}>{item.text || item.commentText}</Text>
        {item.timestamp && (
          <Text style={styles.commentTimestamp}>
            {new Date(item.timestamp).toLocaleString()}
          </Text>
        )}
      </View>
    </View>
  );

  const isInputDisabled = isSelectionMode || isSubmitting || disabled;
  const isButtonDisabled = isInputDisabled || !commentText.trim();

  return (
    <>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.commentSection}>
        
        {showAddComment && (
          <>
            <TextInput
              style={styles.commentInput}
              placeholder={placeholder}
              multiline
              value={commentText}
              onChangeText={setCommentText}
              editable={!isInputDisabled}
            />
            <TouchableOpacity
              style={[
                styles.commentButton, 
                isButtonDisabled && styles.disabledButton
              ]}
              disabled={isButtonDisabled}
              onPress={handleAddComment}
            >
              <Text style={styles.commentButtonText}>Add Comment</Text>
            </TouchableOpacity>
          </>
        )}

        <View style={styles.commentsDisplay}>
          <Text style={styles.commentsTitle}>{commentsTitle}</Text>
          
          {comments && comments.length > 0 ? (
            <FlatList
              data={comments}
              renderItem={renderComment}
              keyExtractor={(item, index) => item.id?.toString() || index.toString()}
              showsVerticalScrollIndicator={false}
              nestedScrollEnabled={true}
            />
          ) : (
            <View style={styles.commentItem}>
              <Image 
                style={styles.userAvatar}
                defaultSource={require('../../assets/images/small_satya_smadhanLogo.png')}
              />
              <View style={styles.commentContent}>
                <Text style={styles.userName}>Sourabh (Police Officer)</Text>
                <Text style={styles.commentText}>The Evidences are been uploaded</Text>
              </View>
            </View>
          )}
          
          {/* Fallback static comments if no dynamic comments provided */}
          {(!comments || comments.length === 0) && (
            <View style={styles.commentItem}>
              <Image 
                style={styles.userAvatar}
                defaultSource={require('../../assets/images/small_satya_smadhanLogo.png')}
              />
              <View style={styles.commentContent}>
                <Text style={styles.userName}>Vashkarjya (Investigating Officer)</Text>
                <Text style={styles.commentText}>Okey, good work</Text>
              </View>
            </View>
          )}
        </View>
      </View>
    </>
  );
};

export default CommentsSection;
