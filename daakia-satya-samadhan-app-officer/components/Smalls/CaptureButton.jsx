import React from 'react';
import { TouchableOpacity, Image, Text, StyleSheet } from 'react-native';

const CaptureButton = ({ onPress }) => {
  return (
    <TouchableOpacity style={styles.captureButton} onPress={onPress}>
      <Image
        source={require('../../assets/images/camera_ic.png')} // Adjust the path as needed
        style={styles.imageIcon}
      />
      <Text style={styles.captureButtonText}>Capture Evidences</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  captureButton: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
    alignSelf: 'center',
  },
  imageIcon: {
    width: 40,
    height: 40,
  },
  captureButtonText: {
    fontSize: 16,
    color: '#333',
    marginTop: 10,
  },
});

export default CaptureButton;