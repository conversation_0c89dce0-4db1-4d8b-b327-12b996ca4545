import React from 'react';
import { View, Text, TextInput, StyleSheet } from 'react-native';

const TitleField = ({ title, onTitleChange,padding,paddingHorizontal }) => {
  return (
    <View style={[styles.formField ,{ padding , paddingHorizontal}]}>
      <Text style={styles.fieldLabel}>
        Title Here <Text style={styles.requiredAsterisk}>*</Text>
      </Text>
      <TextInput
        style={styles.input}
        placeholder="Enter the title..."
        value={title}
        onChangeText={onTitleChange}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  formField: {
    marginBottom: 20,
    padding:20,
    paddingHorizontal:0
    
  },
  fieldLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#333',
    fontFamily: 'Roboto',
  },
  requiredAsterisk: {
    color: 'red',
  },
  input: {
    borderWidth: 1,
    borderColor: '#BFC7D2',
    borderRadius: 8,
    padding: 12,
    textAlignVertical: 'top',
    fontFamily: 'Roboto',
  },
});

export default TitleField;