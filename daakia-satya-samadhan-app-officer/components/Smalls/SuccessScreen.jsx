import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Dimensions, Platform, StatusBar } from 'react-native';
import { AntDesign } from '@expo/vector-icons';
import LottieView from 'lottie-react-native';

const { width, height } = Dimensions.get('window');

const SuccessScreen = ({ message = 'Success!', duration = 2000, onComplete, iconColor = '#4CAF50' }) => {
  const animationRef = useRef(null);
  
  useEffect(() => {

    if (animationRef.current) {
      animationRef.current.play();
    }
    
 
    const timer = setTimeout(() => {
      if (onComplete) onComplete();
    }, duration);
    

    return () => clearTimeout(timer);
  }, [duration, onComplete]);

  return (
    <View style={styles.overlay}>
      <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
      <View style={styles.container}>
        <View style={styles.card}>
          <View style={styles.iconWrapper}>
            {LottieView ? (
              <LottieView
                ref={animationRef}
                source={require('../../assets/animations/success.json')}
                style={styles.lottie}
                autoPlay
                loop={false}
              />
            ) : (
              <AntDesign name="checkcircle" size={70} color={iconColor} />
            )}
          </View>
          <Text style={styles.messageText}>{message}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    zIndex: 9999,
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    ...Platform.select({
      ios: {
        paddingTop: 50,
      },
      android: {
        paddingTop: StatusBar.currentHeight || 0,
      },
    }),
  },
  card: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '90%',
    maxWidth: 340,
  },
  iconWrapper: {
    marginBottom: 20,
    height: 100,
    width: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  lottie: {
    width: 100,
    height: 100,
  },
  messageText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    lineHeight: 28,
    fontFamily: 'Roboto_bold',
  },
});

export default SuccessScreen;