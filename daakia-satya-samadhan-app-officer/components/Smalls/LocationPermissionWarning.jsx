import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, Linking, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import * as IntentLauncher from 'expo-intent-launcher';
import { Colors } from '../../constants/colors';

const LocationPermissionWarning = ({ onPermissionGranted }) => {
  const openLocationSettings = async () => {
    if (Platform.OS === 'ios') {
      Linking.openSettings();
    } else {
      IntentLauncher.startActivityAsync(
        IntentLauncher.ActivityAction.LOCATION_SOURCE_SETTINGS
      );
    }
  };

  const checkLocationPermissions = async () => {
    try {
      const serviceEnabled = await Location.hasServicesEnabledAsync();
      if (!serviceEnabled) {
        Alert.alert(
          'Location Services Disabled',
          'Please enable location services to continue.',
          [
            { text: 'Open Settings', onPress: openLocationSettings },
            { text: 'Cancel', style: 'cancel' }
          ]
        );
        return;
      }

      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Denied',
          'Location permission is required to continue.',
          [
            { text: 'Open Settings', onPress: () => Linking.openSettings() },
            { text: 'Cancel', style: 'cancel' }
          ]
        );
        return;
      }

      onPermissionGranted?.();
    } catch (error) {
      console.error('Error checking location permissions:', error);
    }
  };

  return (
    <View style={styles.locationWarningContainer}>
      <Text style={styles.locationWarningTitle}>
        Location Access Required
      </Text>
      <Text style={styles.locationWarningText}>
        To continue, we need access to your location. This helps in accurately recording the location. Please enable location services and grant permission to continue.
      </Text>
      <TouchableOpacity 
        style={styles.locationCheckButton} 
        onPress={checkLocationPermissions}
      >
        <Ionicons name="location" size={20} color={Colors.background} />
        <Text style={styles.locationCheckButtonText}>
          Enable Location Services
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  locationWarningContainer: {
    backgroundColor: '#FEE2E2',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#DC2626',
  },
  locationWarningTitle: {
    fontFamily: 'Roboto_bold',
    fontSize: 18,
    color: Colors.black,
    marginBottom: 8,
  },
  locationWarningText: {
    fontFamily: 'Roboto',
    fontSize: 14,
    color: Colors.lightText,
    marginBottom: 16,
    lineHeight: 20,
  },
  locationCheckButton: {
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  locationCheckButtonText: {
    fontFamily: 'Roboto_bold',
    color: Colors.background,
    fontSize: 16,
    marginLeft: 8,
  },
});

export default LocationPermissionWarning; 