import React from 'react';
import { View, Text, StyleSheet, useWindowDimensions, TextInput } from 'react-native';

const DescriptionBox = ({ description, onChangeText, placeholder, label  }) => {
  const { width } = useWindowDimensions();
  const isTablet = width >= 768;
  const isWindows = width > 1068;
  
  const styles = StyleSheet.create({
    container: {
      marginTop: 20,
      marginBottom: 10,
      paddingHorizontal: isWindows ? 50 : isTablet ? 10 : 10,
    },
    label: {
      fontSize: 14,
      fontWeight: 'bold',
      color: '#333',
      marginBottom: 4,
      marginBottom: 10,
      fontFamily: 'Roboto',
    },
    descriptionBox: {
      backgroundColor: '#fff',
      padding: 12,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#BFC7D2',
      minHeight: 80,
    },
    descriptionText: {
      fontSize: 14,
      color: '#333',
      fontFamily: 'Roboto',
    },
  });

  return (
    <View style={styles.container}>
      <Text style={styles.label}>
        {label} <Text style={{ color: 'red' }}>*</Text>
      </Text>
      <View style={styles.descriptionBox}>
        <TextInput
          style={styles.descriptionText}
          value={description}
          onChangeText={onChangeText}
          placeholder={placeholder}
          multiline
          numberOfLines={4}
        />
      </View>
    </View>
  );
};

export default DescriptionBox;