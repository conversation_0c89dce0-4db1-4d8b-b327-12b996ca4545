import React from 'react';
import { View, StyleSheet } from 'react-native';
import MediaThumbnail from './MediaThumbnail';


const MediaGrid = ({ media, onDeleteMedia, thumbnailSize }) => {
  return (
    <View style={styles.mediaGrid}>
      {media.map((item, index) => (
        <MediaThumbnail
          key={index}
          uri={item.uri}
          type={item.type}
          compressed={item.compressed}
          onDelete={() => onDeleteMedia(index)}
          thumbnailSize={thumbnailSize} 
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  mediaGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 10,
    justifyContent: 'center',
  },
});

export default MediaGrid;