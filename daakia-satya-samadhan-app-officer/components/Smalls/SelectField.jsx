import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { SelectList } from 'react-native-dropdown-select-list';

const SelectField = ({
  label,
  data,
  onSelect,
  placeholder,
  searchPlaceholder,
  disabled,
  isLoading,
  padding, 
  paddingHorizontal,
  fontSize
}) => {
  return (
    <View style={[styles.container, { padding , paddingHorizontal}]}> 
      {label && <Text style={[styles.label, { fontSize} ]}>{label}</Text>}
      {isLoading ? (
        <ActivityIndicator size="small" color="#0000ff" />
      ) : (
        <SelectList
          setSelected={onSelect}
          data={data}
          save="key"
          placeholder={placeholder}
          searchPlaceholder={searchPlaceholder}
          boxStyles={styles.selectListBox}
          dropdownStyles={styles.selectListDropdown}
          disabled={disabled}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    padding: 0, 
    paddingHorizontal:0,
  },
  label: {
    fontSize: 16,
    marginBottom: 5,
    color: '#333',
    fontFamily: 'Roboto',
  },
  selectListBox: {
    height: 50,
    borderColor: '#BFC7D2',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 15,
    backgroundColor: '#fff',
    fontFamily: 'Roboto',
  },
  selectListDropdown: {
    borderColor: '#c4c4c4c',
    borderWidth: 1,
    borderRadius: 8,
    marginTop: 5,
    fontFamily: 'Roboto',
  },
});

export default SelectField;