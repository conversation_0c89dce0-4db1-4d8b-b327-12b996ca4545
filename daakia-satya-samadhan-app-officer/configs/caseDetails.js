export const getCaseDetailsArray = (caseDetails) => {
  return [
    { label: 'Created By', value: caseDetails?.createdBy?.name || 'N/A' },
    { label: 'Created At', value: caseDetails?.createdAt ? new Date(caseDetails.createdAt).toLocaleString() : 'N/A' },
    { label: 'Title', value: caseDetails?.title || 'N/A' },
    { label: 'Description', value: caseDetails?.description || 'N/A' },
    { label: 'Fir Number', value: caseDetails?.firNumber || 'N/A' },
    { label: 'Case Type', value: caseDetails?.caseType || 'N/A' },
    { label: 'Address 1', value: caseDetails?.address1 || 'N/A' },
    { label: 'Address 2', value: caseDetails?.address2 || 'N/A' },
    { label: 'State', value: caseDetails?.state || 'N/A' },
    { label: 'City', value: caseDetails?.city || 'N/A' },
    { label: 'Pincode', value: caseDetails?.pincode || 'N/A' },
    { label: 'GPS Location', value: caseDetails?.gpsLocation || 'N/A' },
    { label: 'Remarks', value: caseDetails?.remarks || 'N/A' },
  ];
};
